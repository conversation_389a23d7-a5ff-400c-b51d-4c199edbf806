{"repositories": [{"type": "composer", "url": "https://wpackagist.org"}], "require": {"php": ">=8.2", "johnpbloch/wordpress": "6.8.2", "wpackagist-plugin/wp-sentry-integration": "8.3.1", "wpackagist-plugin/wordfence": "8.0.5", "wpackagist-plugin/protect-schemaorg-markup-in-html-editor": "0.6", "wpackagist-plugin/disqus-comment-system": "3.1.2", "wpackagist-plugin/insert-php": "2.4.10", "wpackagist-plugin/redis-cache": "2.6.3", "ext-dom": "*", "ext-libxml": "*"}, "extra": {"wordpress-install-dir": "wordpress"}, "config": {"allow-plugins": {"johnpbloch/wordpress-core-installer": true, "composer/installers": true}}}