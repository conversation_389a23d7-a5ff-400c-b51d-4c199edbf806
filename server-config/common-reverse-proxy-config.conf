# Common proxy settings that can be reused across different proxy configurations
# Include this file in your location blocks with: include /etc/nginx/common.conf;

# Essential headers for proper proxying
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host $host;

# SSL/TLS Configuration
proxy_ssl_protocols TLSv1.2 TLSv1.3;
proxy_ssl_ciphers ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS;
proxy_ssl_server_name on;

# <PERSON>le redirects properly
proxy_redirect off;

# Default timeouts (conservative settings)
proxy_connect_timeout 10s;
proxy_send_timeout 10s;
proxy_read_timeout 10s;

# Buffer settings for better performance
proxy_buffering on;
proxy_buffer_size 4k;
proxy_buffers 8 4k;
proxy_busy_buffers_size 8k;

# Enable HTTP/1.1 keepalive connections to backend
proxy_http_version 1.1;
proxy_set_header Connection "";

# Hide backend server information
proxy_hide_header X-Powered-By;
proxy_hide_header Server;

# Default cache bypass conditions (can be overridden per service)
set $common_bypass 0;
if ($http_cache_control ~* "no-cache|no-store|must-revalidate") {
    set $common_bypass 1;
}

# Common no-cache conditions
proxy_no_cache $http_pragma $http_authorization;