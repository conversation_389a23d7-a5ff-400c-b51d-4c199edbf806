user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 2048;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    server_tokens       off;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    include /etc/nginx/conf.d/*.conf;

    # Increase FastCGI buffer size
    fastcgi_buffers 16 16k;
    fastcgi_buffer_size 32k;
    fastcgi_busy_buffers_size 64k;
    fastcgi_temp_file_write_size 64k;

    # Increase the size of large client headers
    large_client_header_buffers 8 32k;

    # ===========================================
    # PROXY CACHE CONFIGURATION
    # ===========================================

    # Define cache storage
    proxy_cache_path /var/cache/nginx/nginx_cache
                    levels=1:2
                    keys_zone=nginx_cache:10m
                    max_size=100m
                    inactive=60m
                    use_temp_path=off;

    # Cache configuration
    proxy_cache_key "$scheme$request_method$host$request_uri";
    proxy_cache_methods GET HEAD;
    proxy_cache_bypass $http_cache_control;
    proxy_no_cache $http_pragma $http_authorization;


    server {
        listen       80;
        listen       [::]:80;
        server_name  _;
        root         /var/www/html/;
         #Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

         # Include reverse proxy configurations
        include /etc/nginx/reverse-proxy-dev.conf;

        # Rocket-Nginx configuration
        include /etc/nginx/rocket-nginx/conf.d/default.conf;

        error_page 404 /wp-content/themes/homeloanexperts/404.html;
        location = /wp-content/themes/homeloanexperts/{
        }

        ###I think we don't have dedicated page for 500 error so commenting this section
        ### In case we create one in future we can update it here.
        # error_page 500 502 503 504 /50x.html;
        # location = /50x.html {
        # }

        # Enable gzip compression
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript font/ttf font/otf font/eot font/woff font/woff2;
        gzip_vary on;

        # Prevent clickjacking
        add_header X-Frame-Options "DENY";

        # Set the default charset
        charset utf-8;

        # Serve static files directly
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|svg|ttf|otf|eot|webp|avif|html)$ {
            access_log off;
            log_not_found off;
            add_header Cache-Control "public";
            expires max;
        }

        # Disable directory listing
        autoindex off;

        # Remove ETag headers
        add_header ETag "";

        # Expires headers
        location ~* \.(?:css|js|txt|xml|ttf|woff|woff2|eot|ico|jpg|jpeg|png|gif|svg|webp|avif|pdf|mp3|mp4|mov|ogg)$ {
            expires 1M;
            access_log off;
            add_header Cache-Control "public";
        }

        # Rewrite rules for WordPress SEO - XML Sitemap
        rewrite ^/sitemap_index.xml$ /index.php?sitemap=1 last;
        rewrite ^/locations.kml$ /index.php?sitemap=wpseo_local_kml last;
        rewrite ^/geo_sitemap.xml$ /index.php?sitemap=geo last;
        rewrite ^/([^/]+?)-sitemap([0-9]+)?.xml$ /index.php?sitemap=$1&sitemap_n=$2 last;
        rewrite ^/([a-z]+)?-?sitemap.xsl$ /index.php?xsl=$1 last;

        # Redirects
        location = /wp-content/uploads/2010/11/ME-Bank-discharge.pdf {
            return 301 /home-loan-documents/mortgage-discharge-forms/;
        }
        location = /wp-content/uploads/2010/11/Wizard_Discharge_Form.pdf {
            return 301 /home-loan-documents/mortgage-discharge-forms/;
        }
        location = /wp-content/uploads/2010/11/GE_Money_Discharge_Form.pdf {
            return 301 /home-loan-documents/mortgage-discharge-forms/;
        }
        location = /wp-content/uploads/2010/11/BankWest_Discharge_Form.pdf {
            return 301 /home-loan-documents/mortgage-discharge-forms/;
        }

        #Yoast SEO Sitemaps
        location ~ ([^/]*)sitemap(.*).x(m|s)l$ {
            ## this rewrites sitemap.xml to /sitemap_index.xml
            rewrite ^/sitemap.xml$ /sitemap_index.xml permanent;
            ## this makes the XML sitemaps work
            rewrite ^/([a-z]+)?-?sitemap.xsl$ /index.php?yoast-sitemap-xsl=$1 last;
            rewrite ^/sitemap_index.xml$ /index.php?sitemap=1 last;
            rewrite ^/([^/]+?)-sitemap([0-9]+)?.xml$ /index.php?sitemap=$1&sitemap_n=$2 last;
            ## The following lines are optional for the premium extensions
            ## News SEO
            rewrite ^/news-sitemap.xml$ /index.php?sitemap=wpseo_news last;
            ## Local SEO
            rewrite ^/locations.kml$ /index.php?sitemap=wpseo_local_kml last;
            rewrite ^/geo-sitemap.xml$ /index.php?sitemap=wpseo_local last;
            ## Video SEO
            rewrite ^/video-sitemap.xsl$ /index.php?yoast-sitemap-xsl=video last;
        }

        # WordPress
        location / {
            try_files $uri $uri/ /index.php?$args;
        }

        # Pass PHP scripts to FastCGI server
        location ~ \.php$ {
            # include fastcgi_params;
            fastcgi_pass unix:/run/php-fpm/www.sock;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            include fastcgi.conf;
        }

        # Block access to .htaccess files, if Apache's document root concurs with nginx's one
        location ~ /\.ht {
            deny all;
        }
    }
}