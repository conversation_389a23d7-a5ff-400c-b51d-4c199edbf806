# Enquiry Form Proxy Configuration
location /free-quote-proxy/ {
    proxy_pass https://rel290enquiry-form-v3.uat.homeloanexperts.net.au/;

    # Service-specific host header
    proxy_set_header Host rel290enquiry-form-v3.uat.homeloanexperts.net.au;
    
    # Include common proxy settings
    include /etc/nginx/common-reverse-proxy-config.conf;

    # Service-specific SSL configuration
    proxy_ssl_name rel290enquiry-form-v3.uat.homeloanexperts.net.au;
    
    # For development/testing - disable SSL verification (REMOVE in production)
    proxy_ssl_verify off;
    proxy_ssl_verify_depth 1;

    # Service-specific caching configuration
    proxy_cache nginx_cache;

    # Cache HTML for shorter time, static assets longer
    set $cache_time 48h;
    if ($request_uri ~* "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$") {
        set $cache_time 30d;
    }
    
    proxy_cache_valid 200 48h;         # Cache successful responses for 48 hours
    proxy_cache_valid 404 1m;          # Cache 404s for 1 minute
    proxy_cache_valid 301 302 10m;     # <PERSON>ache redirects for 10 minutes

    # Use common bypass logic plus service-specific conditions
    proxy_cache_bypass $common_bypass;
    proxy_no_cache $http_pragma $http_authorization $cookie_sessionid;

    # Service-specific debug headers
    add_header X-Cache-Status $upstream_cache_status;
    add_header X-Cache-Bypass-Reason $common_bypass;
    add_header X-Proxy-Status "browser-friendly-cache";
}