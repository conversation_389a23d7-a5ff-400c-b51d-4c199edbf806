jQuery(document).ready(function () {
    // Prevent comma entry
    jQuery('input[type="number"]').keypress(function (e) {
        if (String.fromCharCode(e.which) === ',') {
            return false;
        }
    });

    // Fade out email messages
    if (jQuery('#emailMessages').length > 0)
        jQuery("#emailMessages").delay(5000).fadeOut(1500);

    // Add comma formatting on blur
    jQuery('#loan_amount').on('blur', function () {
        if (jQuery(this).attr('type') === 'text')
            jQuery(this).val(commafyValue(jQuery(this).val()));
    });

    // Only allow digits and dot
    jQuery('#loan_amount').on("keyup", function () {
        if (jQuery(this).attr('type') === 'text')
            this.value = this.value.replace(/[^0-9]/g, '');
    });
    jQuery('#remain_yrs').on("keyup", function (e) {
         if (jQuery(this).attr('type') === 'text')
            this.value = this.value.replace(/[^0-9]/g, '');
    });
    jQuery('#remain_mths').on("keyup", function () {
        jQuery(this).removeClass('inputError');
        if (jQuery(this).attr('type') === 'text')
             this.value = this.value.replace(/[^0-9]/g, '');
    });
    jQuery('#remain_yrs').on("keyup", function () {
        if (jQuery(this).attr('type') === 'text')
            this.value = this.value.replace(/[^0-9\.]/g, '');
    });
    jQuery('#remain_mths').on("keyup", function () {
        if (jQuery(this).attr('type') === 'text')
            this.value = this.value.replace(/[^0-9\.]/g, '');
    });

    // Select input content on focus
    jQuery('input[type="text"], input[type="number"]').on('click', function () {
        jQuery(this).select();
    });

    // Clear error on radio button click
    jQuery("input[name=contact_broker]").on('click', function () {
        jQuery('#checkAnyOne').html('');
    });

    // Validate remaining years vs term
    jQuery('#remain_yrs').on('blur', function () {
        var term = jQuery('#term').val();
        var remain_yrs = jQuery(this).val();
        if (term !== '' && remain_yrs !== '' && parseInt(remain_yrs) > parseInt(term)) {
            jQuery(this).addClass('inputError');
            alert('Number of years left on your fixed rate cannot be greater than the term (in years)');
            jQuery(this).val(term);
        }

    });

    // ✅ On form submit — validate again
    jQuery('#bccForm').submit(function () {
        if (!validateLoanDetails()) {
            return false;
        }
        jQuery('#loan_amount').val(jQuery('#loan_amount').val().replace(/,/g, ""));
        jQuery('#remain_yrs').val(jQuery('#remain_yrs').val().replace(/,/g, ""));
        jQuery('#remain_mths').val(jQuery('#remain_mths').val().replace(/,/g, ""));
    });

    // ✅ On "Calculate" button click — validate before moving to Slide 2
    jQuery('#bccViewResults').on('click', function (e) {
        var remain_mnths = jQuery('#remain_mths').val();
        if (parseInt(remain_mnths) > 12) {
            alert('Number of remaining months must be less than 12');
        }

        if (!validateLoanDetails()) {
            return false;
        }

        // Show result slide if valid
        jQuery('#\\31').hide();
        jQuery('#\\32').show();
    });

    // ✅ On "Recalculate" — reset Slide 1 and remove errors
    jQuery('#reCalculate').on('click', function () {
        jQuery('#bccForm input').each(function () {
            jQuery(this).removeClass('inputError');
            jQuery(this).closest('.input-group').find('label').removeClass('inputError');
            jQuery(this).closest('.input-group').find('.input-validation').removeClass('inputError').addClass('hidden');
            jQuery(this).closest('.input-group').find('.input-icon--text').removeClass('inputError');
        });

        jQuery('#\\32').hide();
        jQuery('#\\31').show();
    });
});

// ✅ Validation function
function validateLoanDetails() {
    var isValid = true;

    jQuery('input.required').each(function () {
        var $input = jQuery(this);
        var value = $input.val().replace(/,/g, "").trim();

        if (value === '' || isNaN(value) || Number(value) <= 0) {
            $input.addClass('inputError');
            $input.closest('.input-group').find('label').addClass('inputError');
            $input.closest('.input-group').find('.input-validation').addClass('inputError').removeClass('hidden');
            $input.closest('.input-group').find('.input-icon--text').addClass('inputError');
            isValid = false;
        } else {
            // Validate Months
            var remain_mnths = jQuery('#remain_mths').val();
            if (parseInt(remain_mnths) > 12) {
                jQuery('#remain_mths').addClass('inputError');
                isValid = false;
            }
            $input.removeClass('inputError');
            $input.closest('.input-group').find('label').removeClass('inputError');
            $input.closest('.input-group').find('.input-validation').removeClass('inputError').addClass('hidden');
            $input.closest('.input-group').find('.input-icon--text').removeClass('inputError');
        }
    });

    return isValid;
}

// Optional: comma formatting function
function commafyValue(val) {
    val = val.replace(/,/g, '');
    if (!isNaN(val) && val !== '') {
        var parts = val.toString().split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        return parts.join(".");
    }
    return val;
}