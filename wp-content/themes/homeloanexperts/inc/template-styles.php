<?php
require_once ABSPATH . '/wp-content/plugins/lib/verification.php';
function hle_scripts() 
{
    if (is_singular() && comments_open() && get_option("thread_comments"))
    {
        wp_enqueue_script("comment-reply");
    }

    if (is_singular() && wp_attachment_is_image())
    {
        wp_enqueue_script("hle-keyboard-image-navigation", get_template_directory_uri() . "/js/keyboard-image-navigation.js", ["jquery"], "20120202");
    }

    wp_enqueue_script("cookie-handler", get_template_directory_uri() . "/js/cookie-handler.js", "", "20120202");

    /* script to remove gutenberg css files */ 
    wp_dequeue_style("wp-block-library");
    wp_dequeue_style("wp-block-library-theme");

    //DISQUS Comment Copy for Variation Pages with ACF
    if( isset(get_post_meta(get_the_ID(),'disqus_comment_check')[0]) && (!is_null(get_post_meta(get_the_ID(),'disqus_comment_check')[0])) && get_post_meta(get_the_ID(),'disqus_comment_check')[0] != ''){
        $disqus_comment_check =   get_post_meta(get_the_ID(),'disqus_comment_check')[0]; 
    }else{
        $disqus_comment_check = false;
    }
    $obj_data = [];

    if($disqus_comment_check == true){
        if(isset(get_post_meta(get_the_ID(),'disqus_identifier')[0]) && !is_null(get_post_meta(get_the_ID(),'disqus_identifier')[0]) && get_post_meta(get_the_ID(),'disqus_identifier')[0] != ''){
            $disqus_identifier = get_post_meta(get_the_ID(),'disqus_identifier')[0];
        }else{
            $disqus_identifier = '';
        }
        if(isset(get_post_meta(get_the_ID(),'disqus_url')[0]) && !is_null(get_post_meta(get_the_ID(),'disqus_url')[0]) && get_post_meta(get_the_ID(),'disqus_url')[0] != ''){
            $disqus_url = get_post_meta(get_the_ID(),'disqus_url')[0];
        }else{
            $disqus_url = '';
        }

        if($disqus_identifier != '' && $disqus_url != ''){

            $disqus_identifier_array = explode(' ', $disqus_identifier);


            $obj_data['disqus_identifier'] 	= $disqus_identifier;
            $obj_data['disqus_url'] = $disqus_url;
            $obj_data['disqus_title'] = get_the_title($disqus_identifier_array[0]);
            $obj_data['post_id'] = $disqus_identifier_array[0];

            
            wp_enqueue_script('disqus_cmt_copy', get_template_directory_uri() . '/assets/js/disqus_cmt_copy.js', ['jquery'], '1.1.2', true);
            wp_localize_script('disqus_cmt_copy', 'obj_data', $obj_data);
            wp_style_add_data('disqus_cmt_copy', 'preload', 'all');
        }
    }
    
}


add_action('wp_enqueue_scripts', 'hle_chatbot_script', 999999);

function hle_chatbot_script() {  
    // Add Live Chat Script
    wp_enqueue_script('live-chat-script', get_template_directory_uri() . '/chatbots/zopim.js', array('jquery'), '1.0',true);
    // Add Morty Script
    wp_enqueue_script('morty-script', get_template_directory_uri() . '/chatbots/morty.js', array('jquery'), '1.0',true);
}

add_action("wp_enqueue_scripts", "hle_scripts");

/* -------------------------------JS ENQUEUE/DEQUEUE ------------------------------- */

function hle_js_script() {  

        // Dequeue js scripts except SEO page
        if (in_array(get_page_template_slug() , ["interactive-enquire-online-v3.0.php", "interactive-enquire-online-v4.php", "landing-page-FHB-2022.php", "landing-page-IMC.php", "landing-page-new-2022.php", "landing-page-referral.php", "HBI-paid-template-2022.php", 'landing-page-revised/tpl-revised-landing-page-2024.php', 'landing-page-revised/tpl-lp-redesign-2025-v1.php', 'landing-page-revised/tpl-lp-redesign-2025-v2.php']))
        {
            wp_dequeue_script("bootstrapjs", get_template_directory_uri() . "/assets/resources/bootstrap/js/bootstrap.min.js", ["jquery"], false, true);
            wp_dequeue_script("owlcarousel", get_template_directory_uri() . "/assets/resources/owl-carousel/owl.carousel.js", ["jquery"], false, true);
            wp_dequeue_script("owlautoplay", get_template_directory_uri() . "/assets/resources/owl-carousel/owl.autoplay.js", ["jquery"], false, true);
            wp_dequeue_script("owlnavigation", get_template_directory_uri() . "/assets/resources/owl-carousel/owl.navigation.js", ["jquery"], false, true);
            wp_dequeue_script("dynamicscrollspy", get_template_directory_uri() . "/assets/js/dynamicscrollspy.js", ["jquery"], false, true);
            wp_dequeue_script("pagescript", get_template_directory_uri() . "/assets/js/page-scripts.js", ["jquery"], false, true);
        }
  }
  
  add_action('wp_enqueue_scripts', 'hle_js_script');

/* -------------------------------SEO PAGE CSS  ENQUEUE STARTS ------------------------------- */

function hle_seo_script()
{
    //LANDING PAGE NEW 2022
    if(is_page_template("landing-page-new-2022.php")){
        wp_enqueue_style("lp-new-2022", get_template_directory_uri() . "/assets/css/lpnew-springCampaign.css");      
    }
}

add_action("wp_enqueue_scripts", "hle_seo_script");


//For Revised LANDING PAGES
function revised_landing_pages(){
    
    if(is_page_template('landing-page-revised/tpl-revised-landing-page-2024.php')) {

        //Styles   
        wp_enqueue_style('lpr-aos',get_template_directory_uri(). '/landing-page-revised/assets/css/vendor/aos.css');
        wp_enqueue_style('lpr-jquery-ui', get_template_directory_uri(). '/assets/css/jquery-ui.css');
        wp_enqueue_style('lpr-hbi-animate', get_template_directory_uri(). '/HBI-template/css/animate.css');
        wp_enqueue_style('lpr-main', get_template_directory_uri(). '/landing-page-revised/assets/css/landing-page.css');
    
        //Scripts
        wp_enqueue_script('lpr-aos-init', get_template_directory_uri() . '/landing-page-revised/assets/js/aos-init.js', array('jquery'), '1.0',true);
        wp_enqueue_script('lpr-accordion', get_template_directory_uri() . '/landing-page-revised/assets/js/accordion.js', array('lpr-aos-plugin'), '1.0',true);
        wp_enqueue_script('lpr-tooltip', get_template_directory_uri() . '/landing-page-revised/assets/js/tooltip.js', array('lpr-aos-plugin'), '1.0',true);
        wp_enqueue_script('lpr-page-scripts', get_template_directory_uri() . '/landing-page-revised/assets/js/main.js', array('jquery'), '1.0',true);
        wp_enqueue_script('lpr-browser-selctor', get_template_directory_uri() . '/js/css_browser_selector.js', array('jquery'), '1.0',true);

        //Dequeue Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
        wp_dequeue_style('google-fonts');
        wp_dequeue_style('material-icon');
        wp_dequeue_style('material-symbol-outline');

    }


    if(is_page_template('landing-page-revised/tpl-lp-redesign-2025-v1.php')) {

        //Styles   
        wp_enqueue_style('wrp-material-icons-outlined',get_template_directory_uri() . '/hle-website-redesign/assets/icons/material-icons/iconfont/outlined.css');
        wp_enqueue_style('wrp-material-icons-filled',get_template_directory_uri() . '/hle-website-redesign/assets/icons/material-icons/iconfont/filled.css');
        wp_enqueue_style('lpr-aos',get_template_directory_uri(). '/landing-page-revised/assets/css/vendor/aos.css');
       
        wp_enqueue_style('lpr-jquery-ui', get_template_directory_uri(). '/assets/css/jquery-ui.css');
        wp_enqueue_style('lpr-hbi-animate', get_template_directory_uri(). '/HBI-template/css/animate.css');
        wp_enqueue_style('lpr-main', get_template_directory_uri(). '/landing-page-revised/assets/css/lp-variation-one.css');
    
        //Scripts
        wp_enqueue_script('lpr-aos-init', get_template_directory_uri() . '/landing-page-revised/assets/js/aos-init.js', array('jquery'), '1.0',true);
        wp_enqueue_script('lpr-accordion', get_template_directory_uri() . '/landing-page-revised/assets/js/accordion.js', array('lpr-aos-plugin'), '1.0',true);
        wp_enqueue_script('lpr-tooltip', get_template_directory_uri() . '/landing-page-revised/assets/js/tooltip.js', array('lpr-aos-plugin'), '1.0',true);
        wp_enqueue_script('lpr-page-scripts', get_template_directory_uri() . '/landing-page-revised/assets/js/main.js', array('jquery'), '1.0',true);
        wp_enqueue_script('lpr-browser-selctor', get_template_directory_uri() . '/js/css_browser_selector.js', array('jquery'), '1.0',true);

        //Dequeue Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
        wp_dequeue_style('google-fonts');
        wp_dequeue_style('material-icon');
        wp_dequeue_style('material-symbol-outline');

    }
    if(is_page_template('landing-page-revised/tpl-lp-redesign-2025-v2.php')) {

        //Styles  
        wp_enqueue_style('lpr-main', get_template_directory_uri(). '/landing-page-revised/assets/css/lp-variation-two.css');
    
        //Scripts
        wp_enqueue_script('lpr-page-scripts', get_template_directory_uri() . '/landing-page-revised/assets/js/main.js', array('jquery'), '1.0',true);
        wp_enqueue_script('lpr-carousal-scripts', get_template_directory_uri() . '/landing-page-revised/assets/js/owl.carousel.min.js', array('jquery'), '1.0',true);
        wp_enqueue_script('lpr-browser-selctor', get_template_directory_uri() . '/js/css_browser_selector.js', array('jquery'), '1.0',true);

        //Dequeue Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
        wp_dequeue_style('google-fonts');
        wp_dequeue_style('material-icon');
        wp_dequeue_style('material-symbol-outline');

    }

}
add_action("wp_enqueue_scripts", "revised_landing_pages");


//For Website Redesign Project
function website_redesign(){

    //WR - Homepage
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-homepage.php')){

        global $post;

        //Styles        
        wp_enqueue_style('wrp-homepage-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/main.min.css');
        
        //scripts
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        wp_enqueue_script("wrp-matchhight-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/jquery.matchHeight.js", ["jquery"], '0.7.2', true);         
        wp_enqueue_script("wrp-filter-markup", get_template_directory_uri() . "/hle-website-redesign/assets/js/filter-markup.min.js", ["jquery"], '1.0.1', false);         
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);         
        wp_enqueue_script("wrp-main-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/main.min.js", ["jquery"], '1.0.1', true);         
        $data =   ['pageID' => $post->ID];

        wp_localize_script("wrp-main-script", 'data', $data);


        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
    }

    //WR - SEO Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-seo-page.php') || is_page_template('hle-website-redesign/tpl-hle-redesign-refinance-seo-page.php') || is_page_template('hle-website-redesign/tpl-hle-redesign-test-page.php') || is_page_template('search.php')|| is_page_template('hle-website-redesign/tpl-hle-redesign-tree-plantation.php')|| is_page_template('hle-website-redesign/tpl-hle-redesign-sitemap.php')){

        global $post;
        
        wp_enqueue_style('wrp-seo-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/seo.min.css');
        
        //scripts

        wp_enqueue_script("wrp-common-seo-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-mobile-desktop-common.min.js", ["jquery"], '1.0.1', true); 

        if (wp_is_mobile()) {

            // Mobile-specific scripts
            wp_enqueue_script("wrp-main-seo-mobile-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-mobile-specific.min.js", ["jquery"], '1.0.1', true); 
        
        } else {
            //Carousel
            wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
            
            // Desktop-specific scripts
            wp_enqueue_script("wrp-main-seo-desktop-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-desktop-specific.min.js", ["jquery"], '1.0.1', true);
        
        }

        wp_enqueue_script("wrp-main-seo-jumplinks", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-jumplinks.min.js", ["jquery"], '1.0.1', true); 

        wp_enqueue_script("wrp-common-seo-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-mobile-desktop-common.min.js", ["jquery"], '1.0.1', true); 
        
        // Only load calc.min.js on these exact URLs
        $req_path = rtrim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
        $calc_paths = [
            '/interest-only-loans/interest-only-vs-principal-interest-calculator',
            '/home-equity-loans/what-is-cash-out',
        ];

        if (in_array($req_path, $calc_paths, true)) {
            wp_enqueue_script(
                'hle-calc-js',
                get_template_directory_uri() . '/hle-website-redesign/assets/js/calc.min.js',
                ['jquery'],
                '1.0.0',
                true);
        } //Ends

        $HLE_Auth = new HLE_Auth();
        $token = $HLE_Auth->hfc_generate_security_token();
        
        $data =   ['pageID' => $post->ID, 'ajax_url' => admin_url( 'admin-ajax.php' ), 'token' => $token];

        wp_localize_script("wrp-common-seo-script", 'data', $data);

        //360 accessor pages footer link
        $threesixty_accessor_flag = threesixty_accessor_check( get_the_permalink ());
        if($threesixty_accessor_flag){
            wp_enqueue_script("wrp-enquiry-footer-link-change", get_template_directory_uri() . "/hle-website-redesign/assets/js/three-sixty-page-footer.js", ["jquery"], '1.0.1', true);  
        }       

        //AI Summary Data Layer Setup
        $ai_summary_generator_flag = get_post_meta( $post->ID, 'ai_summary_generator_flag', true);
        if($ai_summary_generator_flag){ 
            $ai_data_track = ['pageID' => $post->ID];
            wp_enqueue_script("wrp-ai-summary-data-layer-setup", get_template_directory_uri() . "/hle-website-redesign/assets/data-layer-setup/ai-summary-tracking.js", ["jquery"], '1.0.1', true);  
            wp_localize_script("wrp-ai-summary-data-layer-setup", 'ai_data_track', $ai_data_track);
        }
        
        //2 CTA Button Data Layer Setup
        $two_cta_flag = two_cta_header_test($post->ID);
        if($two_cta_flag){ 
            $two_cta_track = ['pageID' => $post->ID];
            wp_enqueue_script("wrp-two-cta-header-datalayer-setup", get_template_directory_uri() . "/hle-website-redesign/assets/data-layer-setup/two-cta-header-tracking.js", ["jquery"], '1.0.1', true);  
            wp_localize_script("wrp-two-cta-header-datalayer-setup", 'two_cta_track', $two_cta_track);
        }

        //Floating Chatbot Data Layer Setup
        $floating_footer_test_flag = floating_footer_test($post->ID);
        if($floating_footer_test_flag && wp_is_mobile()){ 
            wp_enqueue_script("wrp-floating-chatbot-cta-tracking", get_template_directory_uri() . "/hle-website-redesign/assets/data-layer-setup/floating-chatbot-cta-tracking.js", ["jquery"], '1.0.1', true);  

        }

        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('seo');
        wp_dequeue_style('global-styles-inline');
    }

    //WR - Enquiry Form Template
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-enquiry-form.php')){       

        //WR - Auto Interaction
        wp_enqueue_script("wrp-auto-interact", get_template_directory_uri() . "/hle-website-redesign/assets/js/auto-interact.js", [], '1.1.0', true); 
        
        wp_dequeue_style('seo');
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('new-menu-style');
        wp_dequeue_style('wp-block-library');
        wp_dequeue_style('material-icon');
        wp_dequeue_style('material-symbol-outline');
        wp_dequeue_style('google-fonts');


        wp_deregister_script('jquery');
        wp_deregister_script('jquery-migrate');
        wp_dequeue_script('jquery');
        wp_dequeue_script('jquery-migrate');
        wp_dequeue_script('live-chat-script');
        wp_dequeue_script('morty-script');

    }

    //WR - Blog Single
    if(is_singular('post')){
        
        global $post;
        
        // Check for specific conditions to determine which template to use
        
        $wrs_blog_template = isset(get_post_meta( $post->ID, 'wrs_blog_template')[0]) ? get_post_meta( $post->ID, 'wrs_blog_template')[0] : '';
        
        if (isset($wrs_blog_template) && $wrs_blog_template == 'Website Redesign Single Blog' && $wrs_blog_template != '') { 
    
            wp_enqueue_style('wrp-blog-single-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/blog-single.min.css');

            //scripts
            wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);         
            wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);         
            wp_enqueue_script("wrp-main-blog-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/blog.min.js", ["jquery"], '1.0.1', true);     
            wp_enqueue_script("wrp-filter-markup", get_template_directory_uri() . "/hle-website-redesign/assets/js/filter-markup.min.js", ["jquery"], '1.0.1', false); 

            //Removing Unnecessary Files 
            wp_dequeue_style('classic-theme-styles');
            wp_dequeue_style('google-fonts');
            wp_dequeue_style('theme.css');
            wp_dequeue_style('global-styles-inline');
            wp_dequeue_style('material-icon');
            wp_dequeue_style('material-symbol-outline');
            wp_dequeue_style('seo');
        }
    }   

    //WR - Contact Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-contact-page.php')){
        wp_enqueue_style('wrp-contact-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/contact.min.css');
        
        //scripts
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);         


        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('material-icon');
        wp_dequeue_style('material-symbol-outline');
        
    }

    //WR - About Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-about-page.php')){
        
        wp_enqueue_style('wrp-about-style',get_template_directory_uri() . '/hle-website-redesign/assets/css/about-us.min.css');
        
        //scripts
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);         

        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);         
        wp_enqueue_script("wrp-main-about-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/about-us.min.js", ["jquery"], '1.0.1', true);        



        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        
        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
        wp_dequeue_style('material-icon');
        wp_dequeue_style('material-symbol-outline');
    } 

    //WR - Blog List Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-blog-list.php')){
        
        wp_enqueue_style('wrp-blog-list-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/blog-list.min.css');
        
        
        //scripts
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);         
        wp_enqueue_script("wrp-main-blog-list-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/blog-list.min.js", ["jquery"], '1.0.1', true);        



        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('material-icon-css');
        wp_dequeue_style('material-symbol-outline-css');
    
    }
    
    //WR - Blog List Latest Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-blog-latest.php')){
        
        wp_enqueue_style('wrp-blog-list-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/blog-list.min.css');
     
        //scripts
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);   

        $url_array =    ['ajax_url' => admin_url('admin-ajax.php'), 'nonce' => wp_create_nonce('ajax-nonce')];
              
        wp_enqueue_script("wrp-main-blog-list-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/blog-list.min.js", ["jquery"], '1.0.1', true);        
        wp_localize_script("wrp-main-blog-list-script", 'url', $url_array);


        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('material-icon-css');
        wp_dequeue_style('material-symbol-outline-css');
    }
    
    //WR - Thank you Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-thank-you-page.php')){

        wp_enqueue_style('wrp-than-you-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/thank-you.min.css');
        
        //scripts
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.js", ["jquery"], '1.0.1', true);  

        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
        wp_dequeue_style('material-icon-css');
        wp_dequeue_style('material-symbol-outline-css');
        wp_dequeue_script('live-chat-script');
        wp_dequeue_script('morty-script');
    }
   
    //WR - Blog Category Archive    
    if(is_category()){

        //WR - Blog List Page
        wp_enqueue_style('wrp-blog-list-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/blog-list.min.css');

        //Scripts
        $url_array =    ['ajax_url' => admin_url('admin-ajax.php'), 'nonce' => wp_create_nonce('ajax-nonce')];
        
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        wp_enqueue_script("wrp-main-blog-list-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/blog-list.min.js", ["jquery"], '1.0.1', true);        
        wp_localize_script("wrp-main-blog-list-script", 'url', $url_array);
        

        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
    }  

    //WR - Resources Page Main
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-resources-page.php')){

        //WR - Resources Page
        wp_enqueue_style('wrp-resources-list-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/resources.min.css');

        //resources JS
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);      
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        wp_enqueue_script("wrp-main-resources-list-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/resources.min.js", ["jquery"], '1.0.1', true);        


        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_script('dynamicscrollspy');
        wp_dequeue_script('pagescript');
        wp_dequeue_style('material-icon-css');
        wp_dequeue_style('material-symbol-outline-css');
    }  

    //WR - Resources Page Child
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-resources-child.php')){

        //WR - Resources Page
        wp_enqueue_style('wrp-resources-list-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/resources.min.css');

        //resources JS
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        wp_enqueue_script("wrp-main-resources-child-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/resources-child.min.js", ["jquery"], '1.0.1', true);        


        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_script('dynamicscrollspy');
        wp_dequeue_script('pagescript');
        wp_dequeue_style('material-icon-css');
        wp_dequeue_style('material-symbol-outline-css');
    }  

    //WR - Lender List
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-lenders-list.php')){
       
        wp_enqueue_style('wrp-lender-list-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/lenders-list.min.css');
        
        //scripts
        $url_array =    ['ajax_url' => admin_url('admin-ajax.php'), 'nonce' => wp_create_nonce('ajax-nonce')];
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        wp_enqueue_script("wrp-main-resources-child-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/lender-list.min.js", ["jquery"], '1.0.1', true);        

        wp_enqueue_script("wrp-lenders-load-more", get_template_directory_uri() . "/hle-website-redesign/assets/js/lenders-load-more.min.js", ["jquery"], '1.0.1', true);                
        wp_localize_script("wrp-lenders-load-more", 'url', $url_array);

        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
    }
    //WR - Lenders  Single Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-lender-single.php')){      
        wp_enqueue_style('wrp-lenders-list-single-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/lenders-single.min.css');
        
        //scripts
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        wp_enqueue_script("wrp-lenders-single-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/lenders-single.min.js", ["jquery"], '1.0.1', true);

         //Removing Unnecessary Files 
         wp_dequeue_style('classic-theme-styles');
         wp_dequeue_style('theme.css');
         wp_dequeue_style('global-styles-inline');
         wp_dequeue_style('seo');

    }

    //WR- Our Team Listing Page 
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-our-team-list.php')){
          
        wp_enqueue_style('wrp-team-list-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/team-main.min.css');
    

        //scripts
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        wp_enqueue_script("wrp-team-list", get_template_directory_uri() . "/hle-website-redesign/assets/js/team-list.min.js", ["jquery"], '1.0.1', true);
 
        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
 
    }

    //WR- Our Team Single Page 
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-our-team-single.php')){
        wp_enqueue_style('wrp-team-list-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/team-single.min.css');
        //Scripts 
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        $data =   ['pageID' => $post->ID];
        wp_enqueue_script("wrp-main-seo-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo.min.js", ["jquery"], '1.0.1', true);  
         wp_localize_script("wrp-main-seo-script", 'data', $data);
                // Desktop-specific scripts
        wp_enqueue_script("wrp-main-seo-desktop-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-desktop-specific.js", ["jquery"], '1.0.1', true);

        wp_enqueue_script("wrp-seo-jumplinks", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-jumplinks.min.js", ["jquery"], '1.0.1', true);        

        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
 
    }
  
    //WR- Media Page 
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-media-page.php')){
    
        wp_enqueue_style('wrp-media',get_template_directory_uri() . '/hle-website-redesign/assets/css/media.min.css');
    
        //Scripts
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        $data =   ['pageID' => $post->ID];
        wp_enqueue_script("wrp-main-seo-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo.min.js", ["jquery"], '1.0.1', true);    
        wp_localize_script("wrp-main-seo-script", 'data', $data);

        wp_enqueue_script("wrp-seo-jumplinks", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-jumplinks.min.js", ["jquery"], '1.0.1', true); 
        wp_enqueue_script("wrp-lenders-single-timeline", get_template_directory_uri() . "/hle-website-redesign/assets/js/lenders-single.min.js", ["jquery"], '1.0.1', true);
        // Desktop-specific scripts
        wp_enqueue_script("wrp-main-seo-desktop-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-desktop-specific.js", ["jquery"], '1.0.1', true);    
        
        
        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
 
    }

    //WR- News List Page 
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-news-list.php') || is_post_type_archive('mortgage_post') ){
     
        wp_enqueue_style('wrp-news-list-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/news.min.css');
        
        //Scripts
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
         $url_array =    ['ajax_url' => admin_url('admin-ajax.php'), 'nonce' => wp_create_nonce('ajax-nonce')];
        wp_enqueue_script("wrp-news-load-more", get_template_directory_uri() . "/hle-website-redesign/assets/js/news-list.min.js", ["jquery"], '1.0.1', true);  
        wp_localize_script("wrp-news-load-more", 'url', $url_array);
        
        
        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
 
    }

    //WR - Mortgage News Single
    if(is_singular('mortgage_post')){
        
        global $post;
        
        // Check for specific conditions to determine which template to use
        
        $wrs_blog_template = isset(get_post_meta( $post->ID, 'wrs_mortgage_post_template')[0]) ? get_post_meta( $post->ID, 'wrs_mortgage_post_template')[0] : '';
        
        if (isset($wrs_blog_template) && $wrs_blog_template == 'Website Redesign Mortgage News' && $wrs_blog_template != '') { 
           wp_enqueue_style('wrp-news-single-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/news-single.min.css');

            //scripts
            wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);         
            wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);         
            wp_enqueue_script("wrp-main-blog-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/blog.min.js", ["jquery"], '1.0.1', true);     
            wp_enqueue_script("wrp-filter-markup", get_template_directory_uri() . "/hle-website-redesign/assets/js/filter-markup.min.js", ["jquery"], '1.0.1', false); 

            //Removing Unnecessary Files 
            wp_dequeue_style('classic-theme-styles');
            wp_dequeue_style('google-fonts');
            wp_dequeue_style('theme.css');
            wp_dequeue_style('global-styles-inline');
            wp_dequeue_style('material-icon');
            wp_dequeue_style('material-symbol-outline');
            wp_dequeue_style('seo');
        }
    }   
     
    //WR- Client Stories 
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-client-stories.php')){
     
        wp_enqueue_style('wrp-media',get_template_directory_uri() . '/hle-website-redesign/assets/css/client-stories.min.css');
    

        //Scripts
   
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        $data =   ['pageID' => $post->ID];
        wp_enqueue_script("wrp-main-seo-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo.min.js", ["jquery"], '1.0.1', true);    
        wp_localize_script("wrp-main-seo-script", 'data', $data);

        wp_enqueue_script("wrp-seo-jumplinks", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-jumplinks.min.js", ["jquery"], '1.0.1', true); 
 

        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('seo');
 
    }

    //WR - Testimonial Listing Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-testimonial-list.php')){
           
        //Enquque only for DEV Env
        if(site_url() == 'https://hle.dev.homeloanexperts.net.au/'){
            wp_enqueue_style("wrp-google-fonts",get_template_directory_uri()."/hle-website-redesign/assets/font/Open_Sans/OpenSans.ttf?display=swap");
        }
        wp_enqueue_style('wrp-testimonial-list-page-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/testimonial-listing.min.css');
        
        //scripts
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);
        wp_enqueue_script("wrp-seo-jumplinks", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-jumplinks.min.js", ["jquery"], '1.0.1', true);         
        wp_enqueue_script("wrp-filter-markup", get_template_directory_uri() . "/hle-website-redesign/assets/js/filter-markup.min.js", ["jquery"], '1.0.1', false);  
        wp_enqueue_script("wrp-calc-js", get_template_directory_uri() . "/hle-website-redesign/assets/js/calc.min.js", ["jquery"], '1.0.1', false);   

        $threesixty_accessor_flag = threesixty_accessor_check( get_the_permalink ());
        if($threesixty_accessor_flag){
            wp_enqueue_script("wrp-enquiry-footer-link-change", get_template_directory_uri() . "/hle-website-redesign/assets/js/three-sixty-page-footer.js", ["jquery"], '1.0.1', false);  
        }       


        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
    }
      
    //WR - HLE In the News Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-hle-in-news.php')){
        
        wp_enqueue_style('wrp-in-the-news-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/hle-in-the-news.min.css');
       
        //scripts
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true);  
        $data =   ['pageID' => $post->ID];
        wp_enqueue_script("wrp-main-seo-script", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo.min.js", ["jquery"], '1.0.1', true);       
        wp_localize_script("wrp-main-seo-script", 'data', $data);

        wp_enqueue_script("wrp-seo-jumplinks", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-jumplinks.min.js", ["jquery"], '1.0.1', true); 
        wp_enqueue_script('wrp-owl-carousel-js', get_template_directory_uri() . "/hle-website-redesign/assets/js/owl.carousel.min.js", ["jquery"], '2.3.4', true);



        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_style('material-icon-css');
        wp_dequeue_style('material-symbol-outline-css');
        wp_dequeue_style('seo');
    
    }

    //WR - Glossary Page
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-glossary-page.php')){
         //WR - Resources Page
        wp_enqueue_style('wrp-resources-list-main',get_template_directory_uri() . '/hle-website-redesign/assets/css/glossary.min.css');
        //resources JS     
        wp_enqueue_script("wrp-script-common", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.js", ["jquery"], '1.0.1', true); 
        wp_enqueue_script("wrp-glossary-javascript", get_template_directory_uri() . "/hle-website-redesign/assets/js/glossary.js", ["jquery"], '1.0.1', true); 
        

        //Removing Unnecessary Files 
        wp_dequeue_style('classic-theme-styles');
        wp_dequeue_style('theme.css');
        wp_dequeue_style('global-styles-inline');
        wp_dequeue_script('dynamicscrollspy');
        wp_dequeue_script('pagescript');
        wp_dequeue_style('material-icon-css');
        wp_dequeue_style('material-symbol-outline-css');
    }
    //WR - SEO Variation 2025
    if(is_page_template('hle-website-redesign/tpl-hle-redesign-seo-page-2025.php')){

        global $post;

        $HLE_Auth = new HLE_Auth();
        $token = $HLE_Auth->hfc_generate_security_token();
        
        wp_enqueue_style('wrp-seo-page-2025',get_template_directory_uri() . '/hle-website-redesign/assets/css/seo-2025.min.css');
        wp_enqueue_script("wrp-seo-2025-javascript", get_template_directory_uri() . "/hle-website-redesign/assets/js/seo-2025.js", ["jquery"], '1.0.1', true); 
        
        $data =   ['pageID' => $post->ID, 'ajax_url' => admin_url( 'admin-ajax.php' ), 'token' => $token];
        wp_enqueue_script("wrp-seo-2025-common-javascript", get_template_directory_uri() . "/hle-website-redesign/assets/js/common.min.js", ["jquery"], '1.0.1', true); 
        wp_localize_script("wrp-seo-2025-common-javascript", 'data', $data);
 
    }
}
add_action("wp_enqueue_scripts", "website_redesign");

//Font Preloading for LCP Improvement
function preload_google_fonts() {

    //Preload Fonts to Ensure no CLS is Caused 
       
    echo '<link rel="preload" href="'.get_template_directory_uri().'/hle-website-redesign/assets/font/Open_Sans/open-sans-regular.woff2" as="font" type="font/woff2"  crossorigin="anonymous">';
    echo '<noscript><link rel="preload" href="'.get_template_directory_uri().'/hle-website-redesign/assets/font/Open_Sans/open-sans-regular.woff2" as="font" type="font/woff2"  crossorigin="anonymous"></noscript>';
    
    echo '<link rel="preload" href="'.get_template_directory_uri().'/hle-website-redesign/assets/font/Open_Sans/open-sans-600.woff2" as="font" type="font/woff2"  crossorigin="anonymous">';
    echo '<noscript><link rel="preload" href="'.get_template_directory_uri().'/hle-website-redesign/assets/font/Open_Sans/open-sans-600.woff2" as="font" type="font/woff2"  crossorigin="anonymous"></noscript>';

    echo '<link rel="preload" href="'.get_template_directory_uri().'/hle-website-redesign/assets/font/Open_Sans/open-sans-700.woff2" as="font" type="font/woff2"  crossorigin="anonymous">';
    echo '<noscript><link rel="preload" href="'.get_template_directory_uri().'/hle-website-redesign/assets/font/Open_Sans/open-sans-700.woff2" as="font" type="font/woff2"  crossorigin="anonymous"></noscript>';
    
     
}
add_action('wp_head', 'preload_google_fonts', 1);       
