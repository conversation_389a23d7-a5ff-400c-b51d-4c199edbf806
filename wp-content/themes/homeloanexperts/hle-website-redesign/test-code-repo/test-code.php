<?php

/*------------------- WR SEO Page - TOC Test-----------*/
function wrs_seo_with_toc(){
    global $wpdb;
    //Change Free-Quote Link for selected 360 Home Loan Accessor Pages 
    $toc_array      = $wpdb->get_results("SELECT option_name, option_value FROM $wpdb->dbname.wp_options where option_name like 'options_wr_seo_with_toc_%_pages' ");
    $toc_array_data   = array();
    if(isset($toc_array) && !is_null($toc_array) && is_array($toc_array)){ 
        foreach($toc_array as $row){

            if(str_contains($row->option_name, '_pages')){
                array_push($toc_array_data, $row->option_value);
            }

        }
    }
    $response = 0; 
    if(isset($toc_array_data) && !is_null($toc_array_data) && is_array($toc_array_data)){ 
        if(in_array(get_the_permalink(), $toc_array_data)){
           $response = 1;
        }
    }  
    return $response;
}

/*------------------- WR SEO Page - TOC Test------------*/


/*------------------- WR SEO Page - Remove Product Review------------*/
function remove_product_review($post_id){
    $removal_array =[69750, 4079, 70925, 84088, 3144, 3206, 14984, 4877];
    $removal_flag = 1;
    if(in_array($post_id, $removal_array)){
        $removal_flag = 0;
    }
    return $removal_flag;
}
/*------------------- WR SEO Page - Remove Product Review------------*/


/*------------------- WR SEO Page - Remove Banner Section Test------------*/
function remove_calc_page_banner($post_id){
    $removal_array =[57056, 70925, 3206, 3144];
    $removal_flag = 0;
    if(in_array($post_id, $removal_array)){
        $removal_flag = 1;
    }
    return $removal_flag;
}
/*------------------- WR SEO Page - Remove Banner Section Test------------*/

/*------------------- WR SEO Page - Calculator Page Optimization Test------------*/
function remove_calc_page_test($post_id){
    $removal_array =[4079, 57056, 56601, 56943, 56952, 20783, 15401, 4145, 4849, 70925, 3144, 75100 ];
    $removal_flag = 0;
    if(in_array($post_id, $removal_array)){
        $removal_flag = 1;
    }
    return $removal_flag;
}
/*------------------- WR SEO Page - Calculator Page Optimization Test------------*/

/*------------------- WR SEO Page - Client Stories Child Page Notification------------*/
function client_stories_child_page_notify(){
    global $post;
    if($post->post_parent == 19715)
        echo '<div class="hle-alert hle-alert--warning mt-3">At the time of publication, we helped the customer based on the lending policies available then. Lending criteria and policies can change, so the outcome or options shown here may no longer apply. To explore what’s currently available for your situation, contact our expert team.</div>';
}
/*------------------- WR SEO Page - Client Stories Child Page Notification------------*/

/*------------------- WR FLOATING FOOTER BUTTON----------------------------------*/
function floating_footer_test($post_id){
    $show_array = [35751, 23064, 28378, 343, 127, 2435, 30404];
    $show_flag = 0;
    if(in_array($post_id, $show_array)){
        $show_flag = 1;
    }
    return $show_flag;
}
/*------------------- WR FLOATING FOOTER BUTTON------------*/

/*------------------- WR 2 CTA Header Test------------*/
function two_cta_header_test($post_id){
    $show_array = [2890, 2802, 35751, 60864, 20487];
    $show_flag = 0;
    if(in_array($post_id, $show_array)){
        $show_flag = 1;
    }
    return $show_flag;
}
/*------------------- WR 2 CTA Header Test------------*/
/*------------------- Free Quote Doctors PAGE------------*/
function free_quote_doctors_test($post_id){
    $show_array =[28378, 60864, 59497, 21507, 60301, 23243, 71614, 20512, 20502, 45324, 23249];
    $show_flag = 0;
    if(in_array($post_id, $show_array)){
        $show_flag = 1;
    }
    return $show_flag;
}
/*------------------- Free Quote Doctors PAGE------------*/
/*------------------- Free Quote Guarantors PAGE------------*/
function free_quote_guarantors_test($post_id){
    $show_array =[327, 30605, 30601, 30610, 31108, 30595, 35272, 31265, 64776, 56501];
    $show_flag = 0;
    if(in_array($post_id, $show_array)){
        $show_flag = 1;
    }
    return $show_flag;
}
/*------------------- Free Quote Guarantors PAGE------------*/
