<?php
/*
Template Name: Broker Visiting Card Standalone
*/
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Broker Visiting Card</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet">
<style id="vc-main-style">
*{padding:0;margin:0;box-sizing:border-box;font-family:"Open Sans",Arial,sans-serif}.broker-visiting-card__wrapper{background:#e8f5fb;;display:flex;flex-direction:column;align-items:center;height:100vh;justify-content:center}.broker-visiting-card{display:flex;max-width:800px;width:100%;background:#fff;border-radius:8px;overflow:hidden;box-shadow:0 4px 8px rgb(0 0 0 / .1)}.broker-visiting-card__left{background:#27ADE7;color:#fff;flex:1;padding:40px 20px;text-align:center;display:flex;flex-direction:column;justify-content:center}.broker-visiting-card__logo{max-width:180px;margin:0 auto 20px}.broker-visiting-card__site-link{color:#fff;font-size:20px;text-decoration:none;border-bottom:1px solid currentColor;line-height:1}.broker-visiting-card__right{flex:2;padding:40px 30px;background:#fff}.broker-visiting-card__name{color:#27ADE7;margin-bottom:4px;font-size:24px}.broker-visiting-card__title{color:#515151;opacity:.9;font-size:14px;margin-bottom:20px}.broker-visiting-card__details{list-style:none;padding:0;font-size:16px}.broker-visiting-card__details li{margin-bottom:12px;display:flex;gap:12px}.broker-visiting-card__details li a{color:inherit;text-decoration:none}.broker-visiting-card__download-wrap{margin-top:80px}.broker-visiting-card__download-btn{display:inline-block;padding:18px 32px;border-radius:60px;background:#FB7925;color:#fff;text-decoration:none;font-size:20px;text-transform:uppercase}.broker-visiting-card__download-btn strong{font-weight:700}@media(max-width:767px){.broker-visiting-card__wrapper{padding:80px 20px;height:auto}.broker-visiting-card{flex-direction:column}.broker-visiting-card__left{display:inline-grid}.broker-visiting-card__right{padding:32px 24px}.broker-visiting-card__download-wrap{margin-top:40px}.broker-visiting-card__download-btn{font-size:16px}} @media(max-width:375px){.broker-visiting-card__details{font-size:14px}.broker-visiting-card__right{padding: 32px 16px;}.broker-visiting-card__download-btn{font-size: 14px;}.broker-visiting-card__site-link{font-size:16px}}</style>
</head>
<body><?php
    $broker_name = get_post_meta(get_the_ID(), "vc_broker_name", true);
    $designation = get_post_meta(get_the_ID(), "vc_broker_designation", true);
    $phone_numbers = get_post_meta(get_the_ID(), "vc_phone_numbers", true);
    $fax = get_post_meta(get_the_ID(), "vc_fax", true);
    $address = get_post_meta(get_the_ID(), "vc_address", true);
    $post_box = get_post_meta(get_the_ID(), "vc_post_box_details", true); 
    $broker_email = get_post_meta(get_the_ID(), "vc_broker_email", true); 
    $download_file = get_post_meta(get_the_ID(), "vc_broker_details_download", true);?>
    <div class="broker-visiting-card__wrapper">
      <div class="broker-visiting-card">
         <div class="broker-visiting-card__left">
            <img src="https://www.homeloanexperts.com.au/wp-content/uploads/2025/06/HLE-LOGO-White-1.png" alt="Home Loan Experts Logo" class="broker-visiting-card__logo">
            <a href="https://www.homeloanexperts.com.au" class="broker-visiting-card__site-link">www.homeloanexperts.com.au</a>
         </div>
         <div class="broker-visiting-card__right"><?php
            if($broker_name != '')
                echo '<h2 class="broker-visiting-card__name">'.$broker_name.'</h2>';
            if($designation != '')
                echo '<p class="broker-visiting-card__title">'.$designation.'</p>';
            ?>
            <ul class="broker-visiting-card__details"><?php
                if($phone_numbers != ''){ ?>
                   <li>
                      <span class="icon">
                         <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="10" cy="10" r="10" fill="#27ADE7"/>
                            <path d="M13.975 14.5c-1.042 0-2.07-.227-3.088-.681a9.238 9.238 0 0 1-2.774-1.932A9.236 9.236 0 0 1 6.18 9.113C5.727 8.096 5.5 7.067 5.5 6.025a.509.509 0 0 1 .525-.525H8.05c.117 0 .22.04.313.119.091.079.145.173.162.281l.325 1.75a.882.882 0 0 1-.012.337.526.526 0 0 1-.138.238L7.488 9.45c.166.308.364.606.593.894.23.287.482.564.757.831.258.258.529.498.812.719a7.9 7.9 0 0 0 .9.606l1.175-1.175a.7.7 0 0 1 .294-.169.806.806 0 0 1 .356-.031l1.725.35a.578.578 0 0 1 .287.181.44.44 0 0 1 .113.294v2.025a.509.509 0 0 1-.525.525z" fill="#fff"/>
                         </svg>
                      </span>
                      <span><?php echo $phone_numbers; ?></span>
                   </li><?php
               }  
               if($fax != ''){ ?>
                   <li>
                      <span class="icon">
                         <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="10" cy="10" r="10" fill="#27ADE7"/>
                            <path d="M7.5 15.5a.963.963 0 0 1-.706-.294.963.963 0 0 1-.294-.706v-9c0-.275.098-.51.294-.706A.963.963 0 0 1 7.5 4.5h5c.275 0 .51.098.706.294a.963.963 0 0 1 .294.706v1.55c.15.058.27.15.363.275A.7.7 0 0 1 14 7.75v1a.7.7 0 0 1-.137.425.798.798 0 0 1-.363.275v5.05c0 .275-.098.51-.294.706a.963.963 0 0 1-.706.294h-5zM10 14c.142 0 .26-.048.356-.144a.484.484 0 0 0 .144-.356.484.484 0 0 0-.144-.356A.484.484 0 0 0 10 13a.484.484 0 0 0-.356.144.484.484 0 0 0-.144.356c0 .**************.356A.484.484 0 0 0 10 14z" fill="#fff"/>
                         </svg>
                      </span>
                      <?php echo '<a href="tel:'.$fax.'">'.$fax.'</a>'; ?> 
                   </li><?php
               }
               if($address != ''){ ?>
                   <li>
                        <span class="icon">
                            <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="10" cy="10" r="10" fill="#27ADE7"/>
                                <path d="M10 14.662c-.117 0-.233-.02-.35-.062a.921.921 0 0 1-.313-.188c-.541-.5-1.02-.987-1.437-1.462a10.836 10.836 0 0 1-1.044-1.381 6.576 6.576 0 0 1-.637-1.288A3.542 3.542 0 0 1 6 9.1c0-1.25.402-2.246 1.206-2.987C8.01 5.37 8.942 5 10 5s1.99.37 2.794 1.112C13.598 6.854 14 7.85 14 9.1c0 .375-.073.769-.219 1.181a6.577 6.577 0 0 1-.637 1.288c-.28.446-.627.906-1.044 1.381-.417.475-.896.963-1.438 1.463a.921.921 0 0 1-.312.187 1.034 1.034 0 0 1-.35.063zM10 10c.275 0 .51-.098.706-.294A.963.963 0 0 0 11 9a.963.963 0 0 0-.294-.706A.963.963 0 0 0 10 8a.963.963 0 0 0-.706.294A.963.963 0 0 0 9 9c0 .275.098.51.294.706A.963.963 0 0 0 10 10z" fill="#fff"/>
                            </svg>
                        </span>
                    <?php echo $address; ?>
                   </li><?php
               } 
               if($post_box != ''){ ?>
                   <li>
                      <span class="icon">
                         <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="10" cy="10" r="10" fill="#27ADE7"/>
                            <path d="M6 14C5.725 14 5.48958 13.9021 5.29375 13.7063C5.09792 13.5104 5 13.275 5 13V8C5 7.725 5.09792 7.48958 5.29375 7.29375C5.48958 7.09792 5.725 7 6 7H7V4.5C7 4.35833 7.04792 4.23958 7.14375 4.14375C7.23958 4.04792 7.35833 4 7.5 4H10.5C10.6417 4 10.7604 4.04792 10.8563 4.14375C10.9521 4.23958 11 4.35833 11 4.5V5.5C11 5.64167 10.9521 5.76042 10.8563 5.85625C10.7604 5.95208 10.6417 6 10.5 6H8V9.5C8 9.64167 8.04792 9.76042 8.14375 9.85625C8.23958 9.95208 8.35833 10 8.5 10C8.64167 10 8.76042 9.95208 8.85625 9.85625C8.95208 9.76042 9 9.64167 9 9.5V7H14C14.275 7 14.5104 7.09792 14.7063 7.29375C14.9021 7.48958 15 7.725 15 8V13C15 13.275 14.9021 13.5104 14.7063 13.7063C14.5104 13.9021 14.275 14 14 14H6Z" fill="white"/>
                         </svg>
                      </span>
                      <?php echo $post_box; ?>
                   </li><?php
               }
               if($broker_email != ''){ ?>
                   <li>
                      <span class="icon">
                         <svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="10" cy="10" r="10" fill="#27ADE7"/>
                            <path d="M10 15a4.867 4.867 0 0 1-1.95-.394 5.048 5.048 0 0 1-1.588-1.069 5.05 5.05 0 0 1-1.068-1.587A4.869 4.869 0 0 1 5 10c0-.692.131-1.342.394-1.95a5.05 5.05 0 0 1 1.069-1.588c.45-.45.979-.806 1.587-1.068A4.869 4.869 0 0 1 10 5c.692 0 1.342.131 1.95.394a5.05 5.05 0 0 1 1.588 1.069c.45.45.806.979 1.068 1.587.263.608.394 1.258.394 1.95v.725c0 .492-.169.91-.506 1.256a1.67 1.67 0 0 1-1.244.519 1.87 1.87 0 0 1-.825-.188 1.762 1.762 0 0 1-.65-.537 2.406 2.406 0 0 1-.819.544c-.304.12-.623.181-.956.181a2.41 2.41 0 0 1-1.769-.731A2.41 2.41 0 0 1 7.5 10c0-.692.244-1.281.731-1.769A2.41 2.41 0 0 1 10 7.5a2.41 2.41 0 0 1 1.769.731A2.41 2.41 0 0 1 12.5 10v.725c0 .217.07.4.213.55.141.15.32.225.537.225a.708.708 0 0 0 .537-.225.771.771 0 0 0 .213-.55V10c0-1.117-.387-2.063-1.162-2.838C12.063 6.388 11.117 6 10 6s-2.063.388-2.838 1.162C6.388 7.938 6 8.883 6 10s.388 2.063 1.162 2.838C7.938 13.613 8.883 14 10 14h2c.142 0 .26.048.356.144a.484.484 0 0 1 .144.356c0 .142-.048.26-.144.356A.484.484 0 0 1 12 15h-2zm0-3.5c.417 0 .77-.146 1.063-.438.291-.291.437-.645.437-1.062 0-.417-.146-.77-.438-1.063A1.447 1.447 0 0 0 10 8.5c-.417 0-.77.146-1.063.438A1.447 1.447 0 0 0 8.5 10c0 .417.146.77.438 1.063.291.291.645.437 1.062.437z" fill="#fff"/>
                         </svg>
                      </span>
                      <?php echo '<a href="mailto:'.$broker_email.'">'.$broker_email.'</a>'; ?>
                    </li><?php
                } ?>
            </ul>
         </div>
      </div><?php
        if($download_file != ''){ ?>
          <div class="broker-visiting-card__download-wrap">
             <a href="<?php echo $download_file; ?>" class="broker-visiting-card__download-btn" download>Save to<strong> Contacts</strong></a>
          </div><?php
        } ?>
   </div>
</body>
</html>