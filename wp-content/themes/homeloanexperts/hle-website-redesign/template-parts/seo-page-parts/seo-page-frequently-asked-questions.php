<?php 
$page_ID = get_the_ID();

$wrs_faq_visibility = ( isset(get_post_meta( get_the_ID(),'wrs_faq_visibility')[0] ) ) ? get_post_meta( get_the_ID(),'wrs_faq_visibility')[0]  : false;

//Check for FAQ Visibility Option
if($wrs_faq_visibility == true){

	$section_heading = isset(get_post_meta(get_the_ID(),'wrs_faqs_section_heading')[0]) ? get_post_meta(get_the_ID(),'wrs_faqs_section_heading')[0] : '';
	$faqs = $wpdb->get_results("SELECT meta_key, meta_value FROM $wpdb->dbname.wp_postmeta where meta_key like 'wrs_faqs_list_%' AND post_id = $page_ID");
	$faqs_data   = array();

	$i = 0; $j= 0;
	if( isset($faqs)  && !is_null($faqs) && is_array($faqs) ){

		foreach($faqs as $row){
			if(str_contains($row->meta_key, '_question')){
			$faqs_data[$i]['question'] = $row->meta_value ;
		}
			if(str_contains($row->meta_key, '_answer')){
			$faqs_data[$i]['answer'] = $row->meta_value ;
		}

			$j++;
		if($j % 2 == 0){ $i++; }

		}

	} 
	$count_faqs  = count($faqs_data);  ?>
	<script type="application/ld+json">
		{
			"@context": "https://schema.org",
			"@type": "FAQPage", 
			"mainEntity":[ <?php 
				$i=0;
				foreach($faqs_data as $faq):  
					$faqs_answers = str_replace(array('’'), '', $faq['answer']);                      
					$schema_text = str_replace(array("<p>", "</p>", "<br>", "</br>", "\n"), '', $faqs_answers); ?>
					{ 
						"@type": "Question",
						"name": "<?php echo $faq['question']; ?>",
						"acceptedAnswer": {
							"@type": "Answer",
							"text": <?php echo json_encode( strip_tags( $schema_text ) ); ?>
						}
					}<?php $i++; if($i<$count_faqs){ echo ','; } 
				endforeach; ?> 
			]
		}
	</script>	
	<section class="section section__faq light-blue-bg" id="seo-faqs">
		<div class="container">
            <div class="row">
                <div class="col-lg-9 col-md-8">
                <div class="section__head"><?php
							if($section_heading != ''){ ?>
								<h2 class="section__head--title section__head--faq">
									<?php echo $section_heading; ?>
								</h2><?php
							} ?>
						</div>
                </div>
            </div>
			<div class="row">
					<div class="col-lg-9 col-md-8">
						<?php
						if($count_faqs > 0 && isset($faqs_data) && !is_null($faqs_data) && is_array($faqs_data) ){ ?>
							<div class="faq__accordion" id="faq-section"><?php
								$start = 1;
								foreach($faqs_data as $faq){ ?>
									<div class="faq__accordion--tab <?php if($start == 1){ echo 'openTab'; } ?>">
										<div class="faq__accordion--head">
											<h3 class="faq__accordion--title"><?= $faq['question']; ?></h3>
											<span class="plusminus"></span>
										</div>
										<div class="faq__accordion--body" <?php if($start != 1){ echo 'style=display:none'; } ?>>
											<?= $faq['answer']; ?>
										</div>
									</div><?php
									$start++;
								} //endforeach ?>
							</div><?php
						} //endif ?>
					</div>
					<div class="col-lg-3 col-md-4">
						<div class="support__section support__section--seo" id="sidebar"> 
							<div class="support__section--details"><?php
								//CASE: Free- Quote Doctors or Guarantors (Highest Precedence)
		                           	if(free_quote_doctors_test(get_the_ID()) || free_quote_guarantors_test(get_the_ID())){
		                              if(free_quote_doctors_test(get_the_ID())){
		                                  $link_url = '/free-quote-doctor/'; //For Doctors Niche Pages
		                              }else{
		                                  $link_url = '/free-quote-guarantor-loan/'; //For Guarantor Niche Pages
		                              }
		                           	}else{
		                           		$link_url = '/free-quote/';
		                           	} ?>
								<h3 class="support__section--title">Still need answers?  We're here to help! </h3>
								<a class="btn btn-sm btn-primary--orange btn__has-arrow" href="<?= $link_url; ?>" id="wrd_faq_enquiry_cta">Ask an expert</a>
								<p class="support__section--notice">Our team of mortgage experts will assist you within 24 hours. </p>
							</div>
						</div>
					</div>
			</div>	
		</div>
	</section><?php

}