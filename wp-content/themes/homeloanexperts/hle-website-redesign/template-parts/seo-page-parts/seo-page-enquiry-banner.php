<?php
$wrs_seo_enquiry_main_title = get_option('options_wrs_seo_enquiry_main_title');
$wrs_seo_enquiry_sub_title = get_option('options_wrs_seo_enquiry_sub_title');
$wrs_seo_enquiry_banner_link = get_option('options_wrs_seo_enquiry_banner_link'); ?>
<section class="section section-enquiry-banner bg-dark">
    <div class="container" id="seo-enquiry">
        <div class="row"><?php
            if($wrs_seo_enquiry_main_title != '' || $wrs_seo_enquiry_sub_title != ''){ ?>
                <div class="col-lg-8 col-md-8"><?php
                    if($wrs_seo_enquiry_main_title != ''){ ?>
                        <h2 class="font-weight-normal heading-title text-white">
                            <?= $wrs_seo_enquiry_main_title; ?>
                        </h2><?php
                    }
                    if($wrs_seo_enquiry_sub_title != ''){ ?>
                        <p class="sub-title text-white"> <?= $wrs_seo_enquiry_sub_title; ?> </p><?php
                    } ?>
                </div><?php
            }  ?>
            <div class="col-lg-4 col-md-4 enquiry-action">           
                <div class="btn-grp  btn-grp--column"><?php
                    
                    //Get User's Country 
                    $details = ip_tracker();
                    if (isset($details->country_name) && !is_null($details->country_name)) {

                        $country_name = $details->country_name;
                        if($country_name == 'Australia'){
                            $number = get_option('options_wrs_phone_number_australia');
                        }else{
                            $number = get_option('options_wrs_phone_number_other');
                        }
                    
                    } else{
                            $country_name = '';
                            $number = get_option('options_wrs_phone_number_other');
                    } ?>
                    
                    <a href="tel:<?= $number; ?>" class="btn btn-md btn-outline-primary--blue btn__has-icon btn--width-same" id="wrd_footer_enquiry_call"><svg class="icon" xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 0 24 24" width="20px" fill="#27ade7"><path d="M0 0h24v24H0z" fill="none"/><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/></svg><strong><?= $number; ?></strong></a><?php
                    $fqs_test_pages = [13269, 2435, 13404, 63666, 71307, 70491, 290, 55783, 84408]; 
                        
                    if(isset($wrs_seo_enquiry_banner_link) && !is_null($wrs_seo_enquiry_banner_link) && $wrs_seo_enquiry_banner_link != ''){ 
                            
                        $url = get_the_permalink();

                        $three_sixty_flag = threesixty_accessor_check( $url );

                        //CASE 1: For 360 Specific Pages
                        if($three_sixty_flag){

                            $link = site_url().'/360-home-loan-assessor';
                        
                        }else{

                            $link = isset($wrs_seo_enquiry_banner_link['url']) ? $wrs_seo_enquiry_banner_link['url'] : '/free-quote/'; 

                        }

                        //CASE 2: Free- Quote Static Pages
                        if(in_array(get_the_ID(), $fqs_test_pages)){
                    
                            $link = '/free-quote-static/';
                    
                        }

                        //CASE 3: Free- Quote Doctors or Guarantors (Highest Precedence)
                        if(free_quote_doctors_test(get_the_ID()) || free_quote_guarantors_test(get_the_ID())){
                            if(free_quote_doctors_test(get_the_ID())){
                                $link = '/free-quote-doctor/'; //For Doctors Niche Pages
                            }else{
                                $link = '/free-quote-guarantor-loan/'; //For Guarantor Niche Pages
                            }
                        }

                        $title = (isset($wrs_seo_enquiry_banner_link['title']) && $wrs_seo_enquiry_banner_link['title'] != '')? $wrs_seo_enquiry_banner_link['title'] : 'Get a <strong>free</strong> assessment';  
                        $target = isset($wrs_seo_enquiry_banner_link['target']) ? $wrs_seo_enquiry_banner_link['target'] : '_self';  ?>
                        <a href="<?= $link; ?>" class="btn btn-md btn-primary--orange text-transform-uppercase btn-assessment btn--width-same" id="wrd_footer_enquiry_cta" target="<?= $target; ?>"><?= $title; ?></a><?php
                    } ?>                        
                </div>
            </div>
        </div>
    </div>
</section>