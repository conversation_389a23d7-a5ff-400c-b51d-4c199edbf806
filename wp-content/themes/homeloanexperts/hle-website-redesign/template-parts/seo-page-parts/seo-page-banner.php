<?php
   $banner_type = get_post_meta(get_the_ID(), "wr_select_banner_type", true);
   $banner_title = get_post_meta(get_the_ID(), "wr_banner_main_title", true);
   $banner_sub_title = get_post_meta(get_the_ID(), "wr_banner_sub_title", true);
   
   //Desktop Banner
   $banner_image = ImageData(get_post_meta(get_the_ID(), "tr_banner_image", true));
   
   //Mobile Banner
   $display_mobile_banner = get_post_meta(
       get_the_ID(),
       "wr_banner_image_mobile_enable",
       true
   );
   $banner_image_mobile = ImageData(
       get_post_meta(get_the_ID(), "wr_banner_image_mobile", true)
   );
   
   $read_time = get_post_meta(get_the_ID(), "read_time", true);
   $modified_date = get_the_modified_time("d M, Y");
   $banner_button_link = get_post_meta(get_the_ID(), "wr_banner_banner_link");
   $product_rating = get_option("options_wrs_product_review_rating");
   $product_rating_percentage = ($product_rating / 5) * 100;
   $product_total_no_reviews = get_option(
       "options_wrs_product_review_total_number_of_reviews"
   );
   
   $wr_seo_broker_review = get_post_meta(get_the_ID(), "wr_seo_broker_review", true); ?>
<section class="section-banner section-banner--seo">
   <div class="container">
      <div class="row">
         <div class="col">
            <div class=" banner-card banner-card--bg-secondary">
               <?php if (
                  $banner_type == "Default Banner" ||
                  $banner_type == "Informative Page Banner"
                  ) {
                  /*------------------- WR SEO Page - Remove Banner Image Test-----------*/
                  
                  if (!wp_is_mobile()) { ?> 
               <div class="banner-block banner-block--img-holder <?php if (
                  $banner_type == "Informative Page Banner"
                  ) {
                  echo "banner-block--infoImage";
                  } ?> ">
                  <?php if (
                     isset($banner_image) &&
                     !is_null($banner_image) &&
                     is_array($banner_image)
                     ) { ?>
                  <picture>
                     <?php
                        //For Medium and Large Devices
                        if (
                            isset($banner_image) &&
                            !is_null($banner_image) &&
                            is_array($banner_image)
                        ) { ?>
                     <source srcset="<?= $banner_image[
                        "src"
                        ] ?>" media="(min-width: 990px)"
                         width="471" 
                         height="358" />
                     <?php }
                        if (
                            isset($banner_image) &&
                            !is_null($banner_image) &&
                            is_array($banner_image)
                        ) { ?>
                     <img class="img banner-block--img" src="<?= $banner_image[
                        "src"
                        ] ?>" alt="<?= $banner_image[
                        "alt"
                        ] ?>"  title="<?= $banner_image["title"] ?>" height="358" width="471"/><?php }
                        ?>
                  </picture>
                  <?php } else { ?>
                  <picture>
                     <source srcset="<?= site_url() .
                        "/wp-content/uploads/2024/02/HLE-redesign-hero-banner-mb.webp" ?>" media="(min-width: 500px)" />
                     <img class="img banner-block--img" src="<?= site_url() .
                        "/wp-content/uploads/2024/02/HLE-redesign-hero-banner-mb.webp" ?>" alt="" height="358" width="471" />
                  </picture>
                  <?php } ?>
               </div>
               <?php }
                  } else {
                  
                      //For Interest Rate Banner
                      $rate_interest_rate_heading = get_post_meta(
                          get_the_ID(),
                          "wr_rates_interest_rate_heading"
                      )[0];
                      $rate_interest_rate_shortcode = get_post_meta(
                          get_the_ID(),
                          "wr_rates_interest_rate_shortcode"
                      )[0];
                  
                      $rate_comparison_rate_heading = get_post_meta(
                          get_the_ID(),
                          "wr_rates_comparison_rate_heading"
                      )[0];
                      $rate_comparison_rate_shortcode = get_post_meta(
                          get_the_ID(),
                          "wr_rates_comparison_rate_shortcode"
                      )[0];
                      ?>
               <div class="banner-block banner-block--rate <?php if (
                  $banner_type == "Informative Page Banner"
                  ) {
                  echo "banner-block--infoPage";
                  } ?>">
                  <div class="rate">
                     <?php
                        if (
                            $rate_interest_rate_heading != "" ||
                            $rate_interest_rate_shortcode != ""
                        ) { ?>
                     <div class="rate__comparision">
                        <div class="rate__value">
                           <p class="rate__value--number"><?php if (
                              $rate_interest_rate_shortcode !=
                              ""
                              ) {
                              $rate_interest_rate_shortcode_flag = hle_shortcode_check(
                                  $rate_interest_rate_shortcode
                              );
                              
                              if (
                                  $rate_interest_rate_shortcode_flag ==
                                  true
                              ) {
                                  echo do_shortcode(
                                      $rate_interest_rate_shortcode
                                  );
                              } else {
                                  echo $rate_interest_rate_shortcode;
                              }
                              } ?>
                           </p>
                           <p class="rate__value--time"> %<br/>p.a</p>
                        </div>
                        <p class="rate__name">
                           <?= $rate_interest_rate_heading ?>
                        </p>
                     </div>
                     <?php }
                        if (
                            $rate_comparison_rate_heading != "" ||
                            $rate_comparison_rate_shortcode != ""
                        ) { ?>
                     <div class="rate__comparision">
                        <div class="rate__value">
                           <p class="rate__value--number"><?php if (
                              $rate_comparison_rate_shortcode !=
                              ""
                              ) {
                              $rate_comparison_rate_shortcode_flag = hle_shortcode_check(
                                  $rate_comparison_rate_shortcode
                              );
                              
                              if (
                                  $rate_comparison_rate_shortcode_flag ==
                                  true
                              ) {
                                  echo do_shortcode(
                                      $rate_comparison_rate_shortcode
                                  );
                              } else {
                                  echo $rate_comparison_rate_shortcode;
                              }
                              } ?>
                           </p>
                           <p class="rate__value--time">%<br/>p.a</p>
                        </div>
                        <p class="rate__name">
                           <?= $rate_comparison_rate_heading ?>
                        </p>
                     </div>
                     <?php }
                        ?>
                  </div>
                  <p class="rate__tnc">T&C Applied<sup>*</sup></p>
               </div>
               <?php
                  } ?>
               <div class="banner-block banner-block--content <?php if (
                  $banner_type == "Informative Page Banner"
                  ) {
                  echo "banner-block--infoPage";
                  } ?>">
                  <h1 class="banner-block--title"><?php if (
                     $banner_title != ""
                     ) {
                     echo $banner_title;
                     } else {
                     echo get_the_title();
                     } ?>
                  </h1>
                  <?php
                     if ($banner_sub_title != "") { ?>
                  <p class="banner-block--subtitle">
                     <?= $banner_sub_title ?>
                  </p>
                  <?php }
                     if (
                         isset($banner_button_link[0]) &&
                         !is_null($banner_button_link[0]) &&
                         is_array($banner_button_link[0])
                     ) {
                  
                        $url = get_the_permalink();
                  
                        $three_sixty_flag = threesixty_accessor_check($url);
                  
                        //CASE 1: For 360 Related Pages
                           if ($three_sixty_flag) {
                              $link = site_url() ."/360-home-loan-assessor/";
                           } else {
                              $link = isset($banner_button_link[0]["url"]) ? $banner_button_link[0]["url"] : "/free-quote/";
                           }

                        //CASE 2: Free- Quote Doctors or Guarantors (Highest Precedence)
                           if(free_quote_doctors_test(get_the_ID()) || free_quote_guarantors_test(get_the_ID())){
                              if(free_quote_doctors_test(get_the_ID())){
                                  $link = '/free-quote-doctor/'; //For Doctors Niche Pages
                              }else{
                                  $link = '/free-quote-guarantor-loan/'; //For Guarantor Niche Pages
                              }
                           }
                  
                        $title =
                           isset($banner_button_link[0]["title"]) &&
                           $banner_button_link[0]["title"] != ""
                              ? $banner_button_link[0]["title"]
                              : "GET A <strong>FREE</strong> ASSESSMENT";
                        $target = isset($banner_button_link[0]["target"])
                           ? $banner_button_link[0]["target"]
                           : "_self";
                     
                     if(!two_cta_header_test(get_the_ID())){ ?>
                        <a href="<?= $link ?>" id="wrp_banner-enquiry-cta" class="btn btn-md btn-primary--orange" target="<?= $target ?>" aria-label="<?= $title ?>"><?= $title ?></a><?php
                     
                     }else{ ?>
                        <!-- NEW 2 CTA Banner -->
                        <div class="banner-cta--group">
                           <a href="/360-home-loan-assessor/" id="two-cta-banner-360-free-assessment" class="btn btn-md btn-outline-primary--blue">
                              <span>GET A FREE ASSESSMENT</span> <svg class="navbar-cta__icon" width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><mask id="a" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24"><path fill="#D9D9D9" d="M0 0h24v24H0z"/></mask><g mask="url(#a)"><path d="M5.55 19 2 15.45l1.4-1.4 2.125 2.125 4.25-4.25 1.4 1.425L5.55 19zm0-8L2 7.45l1.4-1.4 2.125 2.125 4.25-4.25 1.4 1.425L5.55 11zM13 17v-2h9v2h-9zm0-8V7h9v2h-9z" fill="#27ADE7"/></g></svg>
                           </a>
                              <a href="<?= $link ?>" id="two-cta-banner-enquire-now" class="btn btn-md btn-primary--orange navbar-cta__dropdown-item navbar-cta__dropdown-item--enquire"><span><?= $title ?></span><svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 0 24 24" width="20px" fill="#fff"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M10.02 6L8.61 7.41 13.19 12l-4.58 4.59L10.02 18l6-6-6-6z"/></svg>
                           </a>
                        </div><?php
                     }
                  } 
                  if (
                        $banner_type != "Informative Page Banner" &&
                        remove_product_review(get_the_ID())
                  ) { ?>
                  <div class="banner-block--review"><?php
                     if(!wp_is_mobile()){ ?>
                        <div class="review-img">
                        <svg width="119" height="24" viewBox="0 0 119 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_3719_252)">
                                <path d="M113.544 8.89866C113.489 8.93047 113.489 8.99409 113.473 9.03385C112.964 10.513 112.455 11.9921 111.954 13.4712C111.915 13.5826 111.868 13.6144 111.759 13.6144C111.391 13.6144 111.015 13.6144 110.639 13.6144C110.553 13.6144 110.514 13.5985 110.483 13.503C109.692 11.1412 108.893 8.77938 108.103 6.4096C108.095 6.37779 108.072 6.34598 108.087 6.29827C108.627 6.29827 109.167 6.29827 109.7 6.29827C109.794 6.29827 109.786 6.36984 109.802 6.41755C110.076 7.30025 110.342 8.19091 110.608 9.08157C110.819 9.77342 111.031 10.4732 111.242 11.173C111.297 11.1253 111.297 11.0617 111.32 11.006C111.821 9.48713 112.322 7.96029 112.816 6.43346C112.855 6.30622 112.909 6.26646 113.043 6.27441C113.395 6.29031 113.747 6.28236 114.099 6.27441C114.201 6.27441 114.24 6.30622 114.272 6.40165C114.773 7.94439 115.281 9.48713 115.783 11.0299C115.798 11.0776 115.814 11.1174 115.845 11.1969C115.978 10.7595 116.103 10.3539 116.229 9.94837C116.589 8.77143 116.941 7.60244 117.293 6.4255C117.317 6.33803 117.348 6.29827 117.45 6.29827C117.959 6.29827 118.468 6.29827 118.992 6.29827C118.812 6.82312 118.648 7.33206 118.475 7.84896C117.849 9.71775 117.215 11.5865 116.589 13.4553C116.55 13.5826 116.495 13.6144 116.37 13.6064C116.01 13.5985 115.657 13.5985 115.297 13.6064C115.188 13.6064 115.133 13.5746 115.101 13.4633C114.6 11.9762 114.092 10.4971 113.583 9.01795C113.567 8.97819 113.551 8.93843 113.536 8.89866H113.544Z" fill="white"/>
                                <path d="M107.132 7.72152H106.921C105.997 7.72152 105.073 7.72152 104.157 7.72152C104.048 7.72152 104.016 7.74538 104.016 7.86466C104.016 8.26228 104.024 8.65989 104.016 9.0575C104.016 9.18474 104.056 9.20064 104.165 9.20064C105.198 9.20064 106.232 9.20064 107.257 9.20064C107.375 9.20064 107.422 9.2245 107.414 9.35174C107.406 9.7255 107.406 10.1072 107.414 10.481C107.414 10.6002 107.383 10.6241 107.273 10.6241C106.24 10.6241 105.198 10.6241 104.165 10.6241C104.04 10.6241 104.009 10.6559 104.009 10.7832C104.016 11.1887 104.016 11.5863 104.009 11.9919C104.009 12.1191 104.04 12.1509 104.165 12.1509C105.363 12.1509 106.568 12.1509 107.766 12.1509C107.907 12.1509 107.938 12.1907 107.931 12.3259C107.923 12.6997 107.931 13.0734 107.931 13.4392C107.931 13.5346 107.915 13.5744 107.805 13.5744C106.06 13.5744 104.314 13.5744 102.568 13.5744C102.451 13.5744 102.435 13.5346 102.435 13.4313C102.435 11.1092 102.435 8.77917 102.435 6.45711C102.435 6.34577 102.466 6.31396 102.576 6.31396C103.891 6.31396 105.214 6.31396 106.537 6.31396C106.631 6.31396 106.678 6.32987 106.709 6.43325C106.842 6.83882 106.983 7.23643 107.116 7.642C107.124 7.66585 107.132 7.69766 107.14 7.73742L107.132 7.72152Z" fill="white"/>
                                <path d="M89.9252 7.72166H87.0522C86.8799 7.72166 86.7938 7.80648 86.7938 7.97613C86.7938 8.34194 86.7938 8.69979 86.7938 9.0656C86.7938 9.16898 86.8251 9.20078 86.9269 9.20078C87.9681 9.20078 89.0093 9.20078 90.0504 9.20078C90.1679 9.20078 90.1992 9.23259 90.1914 9.35188C90.1914 9.73359 90.1914 10.1153 90.1914 10.497C90.1914 10.6083 90.16 10.6242 90.0583 10.6242C89.0171 10.6242 87.9759 10.6242 86.9347 10.6242C86.8173 10.6242 86.786 10.6561 86.7938 10.7753C86.7938 11.1889 86.8017 11.6024 86.7938 12.0159C86.7938 12.1352 86.8251 12.1511 86.9347 12.1511C88.1403 12.1511 89.3459 12.1511 90.5515 12.1511C90.6846 12.1511 90.7159 12.1829 90.7159 12.3181C90.708 12.6918 90.7159 13.0656 90.7159 13.4314C90.7159 13.5348 90.7002 13.5745 90.5828 13.5745C88.837 13.5745 87.0913 13.5745 85.3456 13.5745C85.2438 13.5745 85.2203 13.5507 85.2203 13.4473C85.2203 11.1093 85.2203 8.77136 85.2203 6.42544C85.2203 6.33001 85.2438 6.30615 85.3377 6.30615C86.6686 6.30615 88.0072 6.30615 89.3381 6.30615C89.4007 6.30615 89.4477 6.30615 89.4711 6.38568C89.6199 6.831 89.7686 7.26838 89.9252 7.72961V7.72166Z" fill="white"/>
                                <path d="M30.4135 8.51687C30.4135 7.80116 30.4135 7.0775 30.4135 6.3618C30.4135 6.25047 30.4369 6.21866 30.5544 6.21866C30.9771 6.21866 31.4077 6.22661 31.8304 6.21866C31.9635 6.21866 32.0026 6.24251 32.0026 6.38565C32.0026 7.68983 32.0026 9.00196 32.0026 10.3061C32.0026 10.5208 32.0183 10.7276 32.0653 10.9344C32.2766 11.8568 32.9577 12.1749 33.7327 12.1033C34.5547 12.0238 35.0244 11.5308 35.0949 10.6878C35.1105 10.5288 35.1183 10.3698 35.1183 10.2028C35.1183 8.93039 35.1183 7.65802 35.1183 6.38565C35.1183 6.25842 35.1418 6.20275 35.2827 6.2107C35.7055 6.21866 36.136 6.2107 36.5588 6.2107C36.6605 6.2107 36.6997 6.22661 36.6997 6.34589C36.6997 7.71369 36.6997 9.08148 36.6997 10.4493C36.6997 10.9423 36.6136 11.4274 36.4257 11.8886C36.1126 12.6521 35.5567 13.161 34.7739 13.3996C33.9832 13.6461 33.1769 13.654 32.3862 13.4234C31.3294 13.1133 30.7344 12.3737 30.5074 11.3002C30.4369 10.9662 30.4135 10.6242 30.4135 10.2823C30.4135 9.68585 30.4135 9.09738 30.4135 8.50096V8.51687Z" fill="white"/>
                                <path d="M41.6394 6.09145C42.665 6.09145 43.5809 6.39364 44.3402 7.11729C44.4185 7.18886 44.442 7.23658 44.3559 7.32405C44.0741 7.64214 43.7923 7.96024 43.5261 8.28628C43.4556 8.3658 43.4165 8.38171 43.3304 8.30218C42.9624 7.97614 42.5554 7.72167 42.0622 7.61829C40.8801 7.37177 39.7841 8.09542 39.5571 9.30417C39.3927 10.1789 39.5727 10.9742 40.2303 11.5944C40.8879 12.2227 41.9291 12.3022 42.7667 11.825C42.9938 11.6978 43.2051 11.5308 43.4008 11.3559C43.4713 11.2922 43.5104 11.2843 43.5731 11.3559C43.8471 11.6421 44.1289 11.9284 44.4107 12.2068C44.489 12.2863 44.4733 12.3181 44.4107 12.3976C43.5104 13.3519 42.3988 13.7177 41.1306 13.5666C39.6275 13.3916 38.5629 12.5885 38.0618 11.1332C37.3573 9.09741 38.375 6.7992 40.5826 6.21869C40.7626 6.17097 40.9427 6.13916 41.1228 6.11531C41.295 6.09145 41.4672 6.09145 41.6394 6.0835V6.09145Z" fill="white"/>
                                <path d="M90.7942 6.29799C91.35 6.29799 91.898 6.29799 92.4538 6.29799C92.5477 6.29799 92.5556 6.36161 92.5712 6.41728C92.9078 7.33179 93.2445 8.2463 93.5811 9.16082C93.8551 9.90038 94.1212 10.632 94.3952 11.3716C94.45 11.3397 94.45 11.2841 94.4657 11.2364C95.0528 9.63795 95.64 8.03954 96.2271 6.44113C96.2662 6.3298 96.3132 6.29004 96.4385 6.29004C96.9082 6.29799 97.37 6.29004 97.8397 6.29004C97.8867 6.29004 97.9337 6.29004 98.0041 6.29004C97.8397 6.69561 97.691 7.08527 97.5344 7.47493C96.7438 9.463 95.9531 11.4431 95.1624 13.4312C95.1154 13.5505 95.0607 13.6061 94.9197 13.5982C94.544 13.5823 94.1682 13.5982 93.7924 13.5982C93.7298 13.5982 93.6828 13.5982 93.6594 13.5187C92.7043 11.125 91.7492 8.73139 90.802 6.33775C90.802 6.3298 90.802 6.32185 90.802 6.29004L90.7942 6.29799Z" fill="white"/>
                                <path d="M48.2388 6.21875C49.186 6.21875 50.1333 6.21875 51.0805 6.21875C51.1901 6.21875 51.2214 6.24261 51.2214 6.36189C51.2214 6.76746 51.2214 7.16507 51.2214 7.57064C51.2214 7.68197 51.1901 7.69788 51.0883 7.69788C50.4464 7.69788 49.8123 7.69788 49.1704 7.69788C49.0529 7.69788 49.0216 7.72968 49.0216 7.84897C49.0216 9.67799 49.0216 11.4991 49.0216 13.3281C49.0216 13.4553 48.9981 13.503 48.8651 13.4951C48.4423 13.4871 48.0196 13.4871 47.6047 13.4951C47.4716 13.4951 47.4325 13.4712 47.4325 13.3281C47.4325 11.515 47.4325 9.6939 47.4325 7.88078C47.4325 7.73764 47.4011 7.69788 47.2602 7.69788C46.634 7.70583 45.9999 7.69788 45.3736 7.69788C45.2718 7.69788 45.2327 7.68197 45.2405 7.57064C45.2405 7.16507 45.2405 6.76746 45.2405 6.36189C45.2405 6.24261 45.2796 6.21875 45.3814 6.21875C46.3365 6.21875 47.2837 6.21875 48.2388 6.21875Z" fill="white"/>
                                <path opacity="0.7" d="M89.2128 17.8369C89.4085 17.5188 89.5964 17.2166 89.7843 16.9144C89.9409 16.6599 90.0974 16.4055 90.254 16.143C90.2931 16.0794 90.3401 16.0397 90.4184 16.0397C90.6846 16.0397 90.9429 16.0397 91.2091 16.0397C91.303 16.0397 91.3265 16.0715 91.3265 16.1589C91.3265 17.487 91.3265 18.815 91.3265 20.143C91.3265 20.2385 91.2952 20.2782 91.2012 20.2703C90.9899 20.2703 90.7785 20.2623 90.5593 20.2703C90.4341 20.2703 90.4027 20.2385 90.4027 20.1112C90.4027 19.308 90.4027 18.5128 90.4027 17.7096C90.4027 17.6619 90.4184 17.6142 90.3792 17.5426C89.9878 18.1391 89.6042 18.7275 89.205 19.3319C88.8136 18.7434 88.4378 18.1629 88.0542 17.5744C88.0151 17.6301 88.0307 17.6858 88.0307 17.7335C88.0307 18.5208 88.0307 19.316 88.0307 20.1033C88.0307 20.2305 87.9994 20.2703 87.8742 20.2623C87.655 20.2544 87.4358 20.2623 87.2166 20.2623C87.1305 20.2623 87.107 20.2305 87.107 20.151C87.107 18.815 87.107 17.479 87.107 16.1351C87.107 16.0556 87.1305 16.0238 87.2087 16.0317C87.4827 16.0317 87.7567 16.0317 88.0229 16.0317C88.1168 16.0317 88.1403 16.0874 88.1795 16.143C88.4926 16.652 88.8057 17.1609 89.1189 17.6619C89.1424 17.7017 89.1737 17.7494 89.2128 17.813V17.8369Z" fill="white"/>
                                <path d="M99.108 9.92458C99.108 8.76355 99.108 7.60251 99.108 6.44148C99.108 6.33015 99.1315 6.29834 99.2411 6.29834C99.6873 6.29834 100.133 6.29834 100.58 6.29834C100.666 6.29834 100.697 6.31424 100.697 6.40967C100.697 8.7556 100.697 11.1015 100.697 13.4395C100.697 13.5429 100.658 13.5508 100.58 13.5508C100.141 13.5508 99.6951 13.5508 99.2567 13.5508C99.1236 13.5508 99.108 13.5031 99.108 13.3918C99.108 12.2307 99.108 11.0777 99.108 9.91663V9.92458Z" fill="white"/>
                                <path opacity="0.7" d="M102.216 17.4554C102.216 17.8609 102.224 18.2665 102.216 18.68C102.185 19.6581 101.535 20.3102 100.572 20.35C100.29 20.3579 100.008 20.35 99.7342 20.2784C99.0845 20.0955 98.6382 19.5548 98.5913 18.855C98.5286 17.9564 98.5756 17.0577 98.56 16.1591C98.56 16.0717 98.5991 16.0637 98.6696 16.0637C98.8888 16.0637 99.1158 16.0637 99.335 16.0637C99.4524 16.0637 99.4837 16.0955 99.4837 16.2148C99.4837 16.9305 99.4837 17.6542 99.4837 18.3699C99.4837 18.513 99.4994 18.6482 99.5229 18.7913C99.6325 19.3639 100.063 19.5627 100.556 19.4991C101.01 19.4355 101.284 19.1174 101.292 18.6402C101.3 17.8371 101.3 17.0418 101.292 16.2387C101.292 16.0955 101.331 16.0558 101.464 16.0637C101.676 16.0717 101.879 16.0717 102.091 16.0637C102.2 16.0637 102.224 16.0955 102.224 16.1989C102.224 16.6204 102.224 17.0418 102.224 17.4633L102.216 17.4554Z" fill="white"/>
                                <path opacity="0.7" d="M79.7326 16C80.3119 15.9761 80.8207 16.1511 81.2591 16.5407C81.3452 16.6123 81.3452 16.66 81.2669 16.7395C81.1182 16.8906 80.9773 17.0576 80.8442 17.2246C80.7816 17.3042 80.7346 17.2962 80.6642 17.2326C80.4058 17.002 80.1005 16.8509 79.7561 16.835C79.075 16.8032 78.5113 17.336 78.4722 18.0517C78.4409 18.6799 78.7384 19.2207 79.255 19.4115C79.7874 19.6103 80.2571 19.4672 80.672 19.1014C80.7659 19.0139 80.8207 19.006 80.9068 19.1014C81.0243 19.2445 81.1573 19.3718 81.2983 19.499C81.3765 19.5706 81.3765 19.6103 81.2983 19.6898C80.7738 20.2227 80.1396 20.4135 79.4116 20.3419C78.5505 20.2545 77.8459 19.6819 77.6032 18.8469C77.2196 17.5507 77.9633 16.1988 79.3881 16C79.4977 15.9841 79.6151 16 79.7326 16Z" fill="white"/>
                                <path opacity="0.7" d="M92.673 20.2862C92.5477 20.2862 92.4146 20.2862 92.2894 20.2862C92.2111 20.2862 92.1876 20.2544 92.1876 20.1828C92.1876 19.9283 92.1876 19.6739 92.1876 19.4194C92.1876 19.3478 92.2111 19.324 92.2816 19.324C92.5477 19.324 92.8139 19.324 93.0801 19.324C93.1427 19.324 93.1662 19.3478 93.1662 19.4115C93.1662 19.6739 93.1662 19.9363 93.1662 20.1908C93.1662 20.2782 93.127 20.2942 93.0566 20.2862C92.9313 20.2862 92.7982 20.2862 92.673 20.2862Z" fill="white"/>
                                <path opacity="0.7" d="M76.5699 19.3162C76.6952 19.3162 76.8204 19.3162 76.9378 19.3162C77.0083 19.3162 77.0474 19.3321 77.0474 19.4195C77.0474 19.674 77.0474 19.9364 77.0474 20.1909C77.0474 20.2704 77.0161 20.2943 76.9378 20.2943C76.6873 20.2943 76.429 20.2943 76.1785 20.2943C76.1002 20.2943 76.0767 20.2625 76.0767 20.183C76.0767 19.9285 76.0767 19.682 76.0767 19.4275C76.0767 19.34 76.1159 19.3162 76.1941 19.3162C76.3194 19.3162 76.4447 19.3162 76.5621 19.3162H76.5699Z" fill="white"/>
                                <path d="M72.2877 17.6225C72.2486 17.4793 72.2721 17.3839 72.3582 17.2646C74.0491 14.8869 74.7615 12.2547 74.3701 9.34414C74.0883 7.23679 73.2663 5.3521 71.8728 3.77754C69.1877 0.747722 65.8215 -0.453073 61.8603 0.167205C59.5587 0.525058 57.5625 1.59066 55.9733 3.31631C53.202 6.31432 52.3018 9.84514 53.3664 13.8133C54.6033 18.3859 58.7524 21.5589 63.5121 21.6702C64.9682 21.702 66.3773 21.4396 67.7394 20.8829C67.849 20.8352 67.9351 20.8431 68.0447 20.8988C69.9392 21.9167 71.8415 22.9266 73.736 23.9366C73.783 23.9604 73.8299 23.9843 73.8847 24.0082C73.9004 23.9445 73.8847 23.9127 73.8769 23.873C73.3524 21.7895 72.8279 19.706 72.2956 17.6304L72.2877 17.6225ZM70.9021 20.1831C70.8473 20.1831 70.8082 20.1513 70.769 20.1274C69.8844 19.6583 68.9998 19.1891 68.1152 18.7119C68.0213 18.6642 67.9508 18.6563 67.8569 18.696C67.1445 19.0062 66.4399 19.3163 65.6727 19.4754C62.7527 20.0877 60.1458 19.4276 57.8756 17.4714C56.3491 16.1513 55.394 14.4575 55.0417 12.4614C54.5485 9.69404 55.2218 7.21293 57.0066 5.06581C58.361 3.44355 60.1067 2.44156 62.1734 2.09166C64.8507 1.63838 67.2776 2.29047 69.3756 4.03997C71.0743 5.45548 72.0842 7.29245 72.4208 9.48728C72.7966 11.9684 72.1781 14.2109 70.7064 16.2149C70.542 16.4376 70.3776 16.6523 70.2054 16.875C70.1428 16.9545 70.1271 17.0261 70.1506 17.1215C70.4011 18.0917 70.6438 19.0698 70.8865 20.04C70.9021 20.0877 70.9256 20.1354 70.9021 20.1831Z" fill="white"/>
                                <path d="M21.0115 8.08768C20.2913 6.71989 19.0935 6.14732 17.7549 6.09961C17.1677 6.10756 16.745 6.17118 16.3223 6.30637C13.645 7.18112 12.8778 10.6881 14.9523 12.6046C15.9543 13.535 17.1599 13.7736 18.4751 13.5111C20.9097 13.0181 22.1779 10.3064 21.0115 8.08768ZM19.211 11.4276C18.6238 12.0797 17.8801 12.2706 17.0581 12.0638C16.2283 11.8491 15.7273 11.2765 15.5003 10.4416C15.4455 10.2428 15.4298 10.0439 15.4377 9.87695C15.4377 8.71591 16.1187 7.84116 17.1599 7.62645C18.4281 7.35607 19.6415 8.27854 19.759 9.58271C19.8216 10.2746 19.6728 10.9107 19.211 11.4276Z" fill="white"/>
                                <path d="M11.2808 10.8948C11.2808 10.8948 11.3356 10.863 11.3591 10.855C12.2515 10.4733 12.7525 9.80534 12.8073 8.81925C12.8465 8.17512 12.7212 7.56279 12.2906 7.0618C11.8053 6.50514 11.1712 6.28247 10.4666 6.24271C9.28452 6.18704 8.09459 6.23476 6.9125 6.21885C6.78725 6.21885 6.8029 6.29042 6.8029 6.36199C6.8029 8.69201 6.8029 11.022 6.8029 13.3521C6.8029 13.4554 6.82639 13.4872 6.93599 13.4872C7.37438 13.4872 7.8206 13.4872 8.25899 13.4872C8.37642 13.4872 8.3999 13.4554 8.39208 13.3441C8.39208 12.6523 8.39208 11.9684 8.39208 11.2765C8.39208 11.189 8.40773 11.1572 8.50167 11.1572C8.8383 11.1572 9.17492 11.1572 9.51154 11.1572C9.59765 11.1572 9.65245 11.1811 9.69942 11.2606C10.1613 11.9684 10.631 12.6682 11.1007 13.3759C11.1555 13.4554 11.2103 13.4872 11.3043 13.4872C11.7583 13.4872 12.2045 13.4872 12.6586 13.4872H13.0343C12.4394 12.6125 11.8601 11.7616 11.2808 10.9028V10.8948ZM10.1691 9.73376C9.60548 9.75762 9.04183 9.73376 8.47819 9.74967C8.38425 9.74967 8.39208 9.70196 8.39208 9.63834C8.39208 9.3282 8.39208 9.01806 8.39208 8.69997C8.39208 8.38188 8.39208 8.07969 8.39208 7.7616C8.39208 7.69798 8.39207 7.65822 8.47819 7.65822C9.08098 7.67412 9.67594 7.62641 10.2787 7.68207C10.7641 7.72979 11.1085 8.01607 11.1947 8.42959C11.3434 9.13734 10.9207 9.694 10.177 9.72581L10.1691 9.73376Z" fill="white"/>
                                <path d="M29.1374 8.85068C28.7382 7.37951 27.4621 6.35366 25.8182 6.24233C24.8005 6.17076 23.7828 6.23438 22.7729 6.21847C22.6476 6.21847 22.6163 6.25028 22.6163 6.37752C22.6163 7.5306 22.6163 8.69163 22.6163 9.84471C22.6163 10.9978 22.6163 12.1668 22.6163 13.3278C22.6163 13.4551 22.6476 13.4869 22.7729 13.4869C23.6419 13.4869 24.5108 13.4869 25.3719 13.4869C25.6929 13.4869 26.006 13.4551 26.3192 13.3994C28.4329 13.0018 29.7011 10.958 29.1374 8.85863V8.85068ZM27.4308 10.799C27.0707 11.6181 26.4131 11.9839 25.5598 12.0316C25.1527 12.0554 24.7378 12.0316 24.3229 12.0316C24.229 12.0316 24.2133 11.9998 24.2133 11.9123C24.2133 11.2205 24.2133 10.5366 24.2133 9.84471C24.2133 9.16082 24.2133 8.47692 24.2133 7.79302C24.2133 7.67374 24.2446 7.64988 24.3542 7.64988C24.8083 7.66579 25.2702 7.62603 25.7242 7.67374C26.7732 7.77712 27.5013 8.52463 27.603 9.59819C27.6422 10.0117 27.603 10.4173 27.4386 10.791L27.4308 10.799Z" fill="white"/>
                                <path d="M82.2533 10.9662C82.2533 10.9662 82.3081 10.9344 82.3394 10.9264C83.2162 10.5606 83.7094 9.90851 83.7877 8.95424C83.866 7.92044 83.4902 7.00593 82.4647 6.5606C82.0889 6.40156 81.6975 6.31408 81.2983 6.31408C80.1631 6.30613 79.028 6.31408 77.8929 6.31408C77.8068 6.31408 77.7833 6.33794 77.7833 6.42541C77.7833 8.77134 77.7833 11.1173 77.7833 13.4552C77.7833 13.5507 77.8146 13.5745 77.9007 13.5666C78.3391 13.5666 78.7853 13.5586 79.2237 13.5666C79.3568 13.5666 79.3725 13.5268 79.3725 13.4075C79.3725 12.7316 79.3725 12.0477 79.3725 11.3717C79.3725 11.2684 79.396 11.2445 79.4977 11.2445C79.8265 11.2445 80.1631 11.2445 80.4919 11.2445C80.5859 11.2445 80.6407 11.2763 80.6955 11.3558C81.1574 12.0556 81.6271 12.7554 82.0889 13.4552C82.1437 13.5348 82.1985 13.5666 82.2925 13.5666C82.817 13.5666 83.3415 13.5666 83.866 13.5666C83.9051 13.5666 83.9521 13.5825 83.9991 13.5427C83.4198 12.6918 82.8405 11.8409 82.2612 10.9821L82.2533 10.9662ZM82.1829 8.85086C82.1516 9.36776 81.8071 9.73356 81.2513 9.79718C80.6485 9.8608 80.0457 9.81309 79.4429 9.82104C79.3568 9.82104 79.3646 9.77333 79.3646 9.70971C79.3646 9.39957 79.3646 9.08943 79.3646 8.77134C79.3646 8.4612 79.3646 8.15901 79.3646 7.84887C79.3646 7.7614 79.3803 7.72959 79.4742 7.72959C79.9909 7.72959 80.5154 7.72959 81.0321 7.72959C81.1574 7.72959 81.2826 7.74549 81.4 7.7773C81.9324 7.91249 82.2142 8.28625 82.1829 8.84291V8.85086Z" fill="white"/>
                                <path d="M5.17494 7.13337C4.68958 6.53695 4.03199 6.28247 3.29612 6.24271C2.25494 6.18704 1.20593 6.23476 0.156919 6.21885C0.031664 6.21885 0.00817871 6.25066 0.00817871 6.3779C0.00817871 7.53098 0.00817871 8.69201 0.00817871 9.8451C0.00817871 10.9982 0.00817871 12.1751 0.00817871 13.3441C0.00817871 13.4554 0.031664 13.4872 0.14909 13.4872C0.587483 13.4872 1.01805 13.4793 1.45644 13.4872C1.58169 13.4872 1.59735 13.4475 1.59735 13.3362C1.59735 12.7238 1.59735 12.1195 1.59735 11.5071C1.59735 11.3693 1.66259 11.3004 1.79306 11.3004C2.09837 11.3004 2.41151 11.3004 2.71682 11.3004C3.11607 11.3004 3.50749 11.2606 3.89108 11.1493C5.65248 10.6483 6.33355 8.53297 5.18277 7.12541L5.17494 7.13337ZM3.72668 9.59858C3.484 9.80533 3.17869 9.87691 2.86556 9.87691C2.48196 9.87691 2.09054 9.87691 1.70695 9.87691C1.61301 9.87691 1.58169 9.85305 1.58169 9.74967C1.58169 9.41567 1.58169 9.08963 1.58169 8.75563C1.58169 8.42164 1.58169 8.10355 1.58169 7.7775C1.58169 7.72184 1.55821 7.65027 1.65998 7.65822C2.15317 7.67412 2.63853 7.61846 3.13172 7.69003C3.56229 7.74569 3.92239 7.9445 4.04765 8.40573C4.1729 8.85901 4.09462 9.27253 3.72668 9.59062V9.59858Z" fill="white"/>
                                <path opacity="0.7" d="M85.6352 16.6044C85.0716 16.0478 84.3748 15.8887 83.6155 16.0239C82.3629 16.2466 81.6271 17.3997 81.8541 18.5846C82.0576 19.6661 82.9422 20.3659 84.093 20.3659C84.2652 20.3579 84.4766 20.342 84.688 20.2784C85.4865 20.0398 86.0188 19.5309 86.2223 18.7118C86.4181 17.9086 86.238 17.1929 85.643 16.6124L85.6352 16.6044ZM84.0617 19.4991C83.3337 19.4991 82.7857 18.9265 82.7857 18.1631C82.7857 17.4076 83.3258 16.843 84.0382 16.843C84.7584 16.843 85.3143 17.4235 85.3143 18.179C85.3143 18.9345 84.7819 19.4991 84.0617 19.4991Z" fill="white"/>
                                <path opacity="0.7" d="M97.6988 19.2445C97.2604 18.2107 96.822 17.1769 96.3915 16.1431C96.3523 16.0477 96.3054 16.0159 96.2036 16.0159C96.0079 16.0238 95.8043 16.0159 95.6086 16.0159C95.5147 16.0159 95.4677 16.0477 95.4364 16.1352C95.2564 16.5725 95.0685 17.002 94.8884 17.4393C94.5048 18.3459 94.1212 19.2525 93.7376 20.1511C93.6985 20.2465 93.6907 20.2942 93.8159 20.2862C94.0508 20.2783 94.2856 20.2862 94.5127 20.2862C94.5988 20.2862 94.6379 20.2624 94.6692 20.1829C94.7632 19.9363 94.8728 19.6898 94.9667 19.4433C94.998 19.3638 95.0372 19.3399 95.1233 19.3399C95.6399 19.3399 96.1645 19.3399 96.6811 19.3399C96.7672 19.3399 96.8064 19.3717 96.8299 19.4433C96.9238 19.6898 97.0256 19.9363 97.1273 20.1829C97.1508 20.2306 97.1665 20.2862 97.2369 20.2862C97.5266 20.2862 97.8241 20.2862 98.1372 20.2862C97.9806 19.9204 97.8397 19.5864 97.6988 19.2524V19.2445ZM96.368 18.5208C96.0549 18.5208 95.7339 18.5208 95.4208 18.5208C95.3738 18.5208 95.3425 18.5049 95.366 18.4493C95.5382 18.0278 95.7104 17.5984 95.8983 17.1451C96.0783 17.5984 96.2506 18.0198 96.4228 18.4413C96.4463 18.5049 96.4228 18.5208 96.368 18.5208Z" fill="white"/>
                                <path d="M68.4362 6.42529C68.6397 6.76724 68.7493 7.14895 68.8511 7.53066C69.1329 8.50084 69.2895 9.49488 69.3991 10.4969C69.4852 11.3319 69.54 12.1668 69.54 13.0018C69.54 13.1052 69.5087 13.1847 69.4382 13.2563C68.8824 13.805 68.2561 14.2662 67.6455 14.7354C66.7374 15.4273 65.7745 16.0476 64.7646 16.5883C64.7177 16.6122 64.6629 16.6599 64.6002 16.644C64.5533 16.6042 64.5376 16.5406 64.5298 16.4849C64.1775 14.1549 63.8252 11.8328 63.4808 9.50283C63.4729 9.43921 63.4573 9.38354 63.4729 9.31993C63.4886 9.21655 63.5747 9.17678 63.653 9.13702C64.2323 8.80303 64.8038 8.47698 65.3831 8.14299C66.1268 7.72152 66.8627 7.29209 67.6064 6.87062C67.8412 6.73543 68.0761 6.6082 68.3031 6.47301C68.3501 6.44915 68.397 6.4412 68.444 6.42529H68.4362Z" fill="white" fill-opacity="0.9"/>
                                <path d="M63.4886 9.28833C63.8565 11.7456 64.2245 14.1949 64.5924 16.6522C62.9954 16.3738 61.4923 15.8013 60.0206 15.1412C59.6292 14.9663 59.2534 14.7754 58.862 14.6005C58.7602 14.5527 58.7132 14.4891 58.6741 14.3857C58.3923 13.5189 58.2044 12.6362 58.0557 11.7376C57.8678 10.6323 57.7895 9.51895 57.8208 8.40563C57.8208 8.30225 57.8208 8.19092 57.8521 8.09549C57.9147 8.05573 57.9774 8.07959 58.04 8.09549C59.4961 8.40563 60.9443 8.71577 62.4004 9.02591C62.7136 9.08953 63.0189 9.15314 63.332 9.22471C63.3946 9.24062 63.4651 9.22471 63.4964 9.31219L63.4886 9.28833Z" fill="white" fill-opacity="0.8"/>
                                <path d="M63.4886 9.2883C63.0345 9.18492 62.5883 9.0895 62.1343 8.99407C61.1166 8.77936 60.0989 8.56465 59.0734 8.34198C58.6663 8.25451 58.2592 8.16703 57.8443 8.07956C57.8678 8.0557 57.8834 8.03184 57.9069 8.01594C59.0655 7.23661 60.232 6.4891 61.5002 5.91653C61.735 5.81315 61.9777 5.73363 62.2126 5.63025C62.6588 5.43144 63.1207 5.40759 63.5904 5.4394C65.1952 5.55868 66.7452 5.90858 68.2796 6.38572C68.3266 6.40162 68.3735 6.41753 68.4205 6.43343C67.896 6.74357 67.3637 7.04576 66.8392 7.34795C65.7432 7.97618 64.655 8.59645 63.5591 9.22469C63.5277 9.24059 63.5043 9.26445 63.4729 9.2883H63.4886Z" fill="white"/>
                                </g>
                                <defs>
                                <clipPath id="clip0_3719_252">
                                <rect width="119" height="24" fill="white"/>
                                </clipPath>
                                </defs>
                            </svg>
                        </div><?php
                     } ?>
                     <div class="review--info d-flex">
                        <div class="review-star">
                           <svg width="95" height="18" viewBox="0 0 95 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <mask id="path-1-inside-1_4485_2902" fill="white">
                                 <path fill-rule="evenodd" clip-rule="evenodd" d="M9.17853 0.859375L11.1992 7.07822H17.738L12.448 10.9217L14.4686 17.1405L9.17853 13.2971L3.88846 17.1405L5.90909 10.9217L0.619019 7.07822H7.1579L9.17853 0.859375ZM28.3035 0.859375L30.3242 7.07822H36.863L31.573 10.9217L33.5936 17.1405L28.3035 13.2971L23.0135 17.1405L25.0341 10.9217L19.744 7.07822H26.2829L28.3035 0.859375ZM49.4492 7.07822L47.4285 0.859375L45.4079 7.07822H38.869L44.1591 10.9217L42.1385 17.1405L47.4285 13.2971L52.7186 17.1405L50.698 10.9217L55.988 7.07822H49.4492ZM66.5535 0.859375L68.5742 7.07822H75.113L69.823 10.9217L71.8436 17.1405L66.5535 13.2971L61.2635 17.1405L63.2841 10.9217L57.994 7.07822H64.5329L66.5535 0.859375ZM87.6992 7.07822L85.6785 0.859375L83.6579 7.07822H77.119L82.4091 10.9217L80.3885 17.1405L85.6785 13.2971L90.9686 17.1405L88.948 10.9217L94.238 7.07822H87.6992Z"/>
                              </mask>
                              <linearGradient id="grad1" x1="0%" x2="100%" y1="0%" y2="0%">
                                 <!-- CALCULATE THE PERCENTAGE OF FILL ACCORDING TO THE NEED -->
                                 <stop offset="<?= $product_rating_percentage ?>%" stop-color="white" />
                                 <stop offset="<?= $product_rating_percentage ?>%" stop-color="#1380af" />
                              </linearGradient>
                              <path  fill="url(#grad1)" fill-rule="evenodd" clip-rule="evenodd" d="M8.55951 0.859375L10.5801 7.07822H17.119L11.8289 10.9217L13.8496 17.1405L8.55951 13.2971L3.26944 17.1405L5.29007 10.9217L0 7.07822H6.53888L8.55951 0.859375ZM27.6845 0.859375L29.7051 7.07822H36.244L30.954 10.9217L32.9746 17.1405L27.6845 13.2971L22.3944 17.1405L24.4151 10.9217L19.125 7.07822H25.6639L27.6845 0.859375ZM48.8301 7.07822L46.8095 0.859375L44.7889 7.07822H38.25L43.5401 10.9217L41.5194 17.1405L46.8095 13.2971L52.0996 17.1405L50.0789 10.9217L55.369 7.07822H48.8301ZM65.9345 0.859375L67.9551 7.07822H74.494L69.2039 10.9217L71.2246 17.1405L65.9345 13.2971L60.6444 17.1405L62.6651 10.9217L57.375 7.07822H63.9139L65.9345 0.859375ZM87.0801 7.07822L85.0595 0.859375L83.0389 7.07822H76.5L81.7901 10.9217L79.7694 17.1405L85.0595 13.2971L90.3496 17.1405L88.3289 10.9217L93.619 7.07822H87.0801Z" fill="white"/>
                              <path  d="M11.1992 7.07822L10.7236 7.23273L10.8359 7.57822H11.1992V7.07822ZM9.17853 0.859375L9.65406 0.704866L9.17853 -0.758659L8.703 0.704866L9.17853 0.859375ZM17.738 7.07822L18.0319 7.48273L19.2769 6.57822H17.738V7.07822ZM12.448 10.9217L12.1541 10.5172L11.8602 10.7307L11.9724 11.0762L12.448 10.9217ZM14.4686 17.1405L14.1747 17.545L15.4197 18.4495L14.9441 16.986L14.4686 17.1405ZM9.17853 13.2971L9.47242 12.8926L9.17853 12.679L8.88463 12.8926L9.17853 13.2971ZM3.88846 17.1405L3.41293 16.986L2.9374 18.4495L4.18235 17.545L3.88846 17.1405ZM5.90909 10.9217L6.38461 11.0762L6.49687 10.7307L6.20298 10.5172L5.90909 10.9217ZM0.619019 7.07822V6.57822H-0.919823L0.325126 7.48273L0.619019 7.07822ZM7.1579 7.07822V7.57822H7.52117L7.63343 7.23273L7.1579 7.07822ZM30.3242 7.07822L29.8486 7.23273L29.9609 7.57822H30.3242V7.07822ZM28.3035 0.859375L28.7791 0.704866L28.3035 -0.758659L27.828 0.704866L28.3035 0.859375ZM36.863 7.07822L37.1569 7.48273L38.4019 6.57822H36.863V7.07822ZM31.573 10.9217L31.2791 10.5172L30.9852 10.7307L31.0974 11.0762L31.573 10.9217ZM33.5936 17.1405L33.2997 17.545L34.5447 18.4495L34.0691 16.986L33.5936 17.1405ZM28.3035 13.2971L28.5974 12.8926L28.3035 12.679L28.0096 12.8926L28.3035 13.2971ZM23.0135 17.1405L22.5379 16.986L22.0624 18.4495L23.3074 17.545L23.0135 17.1405ZM25.0341 10.9217L25.5096 11.0762L25.6219 10.7307L25.328 10.5172L25.0341 10.9217ZM19.744 7.07822V6.57822H18.2052L19.4501 7.48273L19.744 7.07822ZM26.2829 7.07822V7.57822H26.6462L26.7584 7.23273L26.2829 7.07822ZM47.4285 0.859375L47.9041 0.704866L47.4285 -0.758659L46.953 0.704866L47.4285 0.859375ZM49.4492 7.07822L48.9736 7.23273L49.0859 7.57822H49.4492V7.07822ZM45.4079 7.07822V7.57822H45.7712L45.8834 7.23273L45.4079 7.07822ZM38.869 7.07822V6.57822H37.3302L38.5751 7.48273L38.869 7.07822ZM44.1591 10.9217L44.6346 11.0762L44.7469 10.7307L44.453 10.5172L44.1591 10.9217ZM42.1385 17.1405L41.6629 16.986L41.1874 18.4495L42.4324 17.545L42.1385 17.1405ZM47.4285 13.2971L47.7224 12.8926L47.4285 12.679L47.1346 12.8926L47.4285 13.2971ZM52.7186 17.1405L52.4247 17.545L53.6697 18.4495L53.1941 16.986L52.7186 17.1405ZM50.698 10.9217L50.4041 10.5172L50.1102 10.7307L50.2224 11.0762L50.698 10.9217ZM55.988 7.07822L56.2819 7.48273L57.5269 6.57822H55.988V7.07822ZM68.5742 7.07822L68.0986 7.23273L68.2109 7.57822H68.5742V7.07822ZM66.5535 0.859375L67.0291 0.704866L66.5535 -0.758658L66.078 0.704866L66.5535 0.859375ZM75.113 7.07822L75.4069 7.48273L76.6519 6.57822H75.113V7.07822ZM69.823 10.9217L69.5291 10.5172L69.2352 10.7307L69.3474 11.0762L69.823 10.9217ZM71.8436 17.1405L71.5497 17.545L72.7947 18.4495L72.3191 16.986L71.8436 17.1405ZM66.5535 13.2971L66.8474 12.8926L66.5535 12.679L66.2596 12.8926L66.5535 13.2971ZM61.2635 17.1405L60.7879 16.986L60.3124 18.4495L61.5574 17.545L61.2635 17.1405ZM63.2841 10.9217L63.7596 11.0762L63.8719 10.7307L63.578 10.5172L63.2841 10.9217ZM57.994 7.07822V6.57822H56.4552L57.7001 7.48273L57.994 7.07822ZM64.5329 7.07822V7.57822H64.8962L65.0084 7.23273L64.5329 7.07822ZM85.6785 0.859375L86.1541 0.704866L85.6785 -0.758656L85.203 0.704866L85.6785 0.859375ZM87.6992 7.07822L87.2236 7.23273L87.3359 7.57822H87.6992V7.07822ZM83.6579 7.07822V7.57822H84.0212L84.1334 7.23273L83.6579 7.07822ZM77.119 7.07822V6.57822H75.5802L76.8251 7.48273L77.119 7.07822ZM82.4091 10.9217L82.8846 11.0762L82.9969 10.7307L82.703 10.5172L82.4091 10.9217ZM80.3885 17.1405L79.9129 16.986L79.4374 18.4495L80.6824 17.545L80.3885 17.1405ZM85.6785 13.2971L85.9724 12.8926L85.6785 12.679L85.3846 12.8926L85.6785 13.2971ZM90.9686 17.1405L90.6747 17.545L91.9197 18.4495L91.4441 16.986L90.9686 17.1405ZM88.948 10.9217L88.6541 10.5172L88.3602 10.7307L88.4724 11.0762L88.948 10.9217ZM94.238 7.07822L94.5319 7.48273L95.7769 6.57822H94.238V7.07822ZM11.6747 6.92371L9.65406 0.704866L8.703 1.01388L10.7236 7.23273L11.6747 6.92371ZM17.738 6.57822H11.1992V7.57822H17.738V6.57822ZM12.7419 11.3262L18.0319 7.48273L17.4441 6.67371L12.1541 10.5172L12.7419 11.3262ZM14.9441 16.986L12.9235 10.7672L11.9724 11.0762L13.9931 17.295L14.9441 16.986ZM8.88463 13.7016L14.1747 17.545L14.7625 16.736L9.47242 12.8926L8.88463 13.7016ZM4.18235 17.545L9.47242 13.7016L8.88463 12.8926L3.59457 16.736L4.18235 17.545ZM5.43356 10.7672L3.41293 16.986L4.36399 17.295L6.38461 11.0762L5.43356 10.7672ZM0.325126 7.48273L5.61519 11.3262L6.20298 10.5172L0.912911 6.67371L0.325126 7.48273ZM7.1579 6.57822H0.619019V7.57822H7.1579V6.57822ZM8.703 0.704866L6.68237 6.92371L7.63343 7.23273L9.65406 1.01388L8.703 0.704866ZM30.7997 6.92371L28.7791 0.704866L27.828 1.01388L29.8486 7.23273L30.7997 6.92371ZM36.863 6.57822H30.3242V7.57822H36.863V6.57822ZM31.8669 11.3262L37.1569 7.48273L36.5691 6.67371L31.2791 10.5172L31.8669 11.3262ZM34.0691 16.986L32.0485 10.7672L31.0974 11.0762L33.1181 17.295L34.0691 16.986ZM28.0096 13.7016L33.2997 17.545L33.8875 16.736L28.5974 12.8926L28.0096 13.7016ZM23.3074 17.545L28.5974 13.7016L28.0096 12.8926L22.7196 16.736L23.3074 17.545ZM24.5586 10.7672L22.5379 16.986L23.489 17.295L25.5096 11.0762L24.5586 10.7672ZM19.4501 7.48273L24.7402 11.3262L25.328 10.5172L20.0379 6.67371L19.4501 7.48273ZM26.2829 6.57822H19.744V7.57822H26.2829V6.57822ZM27.828 0.704866L25.8074 6.92371L26.7584 7.23273L28.7791 1.01388L27.828 0.704866ZM46.953 1.01388L48.9736 7.23273L49.9247 6.92371L47.9041 0.704866L46.953 1.01388ZM45.8834 7.23273L47.9041 1.01388L46.953 0.704866L44.9324 6.92371L45.8834 7.23273ZM38.869 7.57822H45.4079V6.57822H38.869V7.57822ZM44.453 10.5172L39.1629 6.67371L38.5751 7.48273L43.8652 11.3262L44.453 10.5172ZM42.614 17.295L44.6346 11.0762L43.6836 10.7672L41.6629 16.986L42.614 17.295ZM47.1346 12.8926L41.8446 16.736L42.4324 17.545L47.7224 13.7016L47.1346 12.8926ZM53.0125 16.736L47.7224 12.8926L47.1346 13.7016L52.4247 17.545L53.0125 16.736ZM50.2224 11.0762L52.2431 17.295L53.1941 16.986L51.1735 10.7672L50.2224 11.0762ZM55.6941 6.67371L50.4041 10.5172L50.9919 11.3262L56.2819 7.48273L55.6941 6.67371ZM49.4492 7.57822H55.988V6.57822H49.4492V7.57822ZM69.0497 6.92371L67.0291 0.704866L66.078 1.01388L68.0986 7.23273L69.0497 6.92371ZM75.113 6.57822H68.5742V7.57822H75.113V6.57822ZM70.1169 11.3262L75.4069 7.48273L74.8191 6.67371L69.5291 10.5172L70.1169 11.3262ZM72.3191 16.986L70.2985 10.7672L69.3474 11.0762L71.3681 17.295L72.3191 16.986ZM66.2596 13.7016L71.5497 17.545L72.1375 16.736L66.8474 12.8926L66.2596 13.7016ZM61.5574 17.545L66.8474 13.7016L66.2596 12.8926L60.9696 16.736L61.5574 17.545ZM62.8086 10.7672L60.7879 16.986L61.739 17.295L63.7596 11.0762L62.8086 10.7672ZM57.7001 7.48273L62.9902 11.3262L63.578 10.5172L58.2879 6.67371L57.7001 7.48273ZM64.5329 6.57822H57.994V7.57822H64.5329V6.57822ZM66.078 0.704866L64.0574 6.92371L65.0084 7.23273L67.0291 1.01388L66.078 0.704866ZM85.203 1.01388L87.2236 7.23273L88.1747 6.92371L86.1541 0.704866L85.203 1.01388ZM84.1334 7.23273L86.1541 1.01388L85.203 0.704866L83.1824 6.92371L84.1334 7.23273ZM77.119 7.57822H83.6579V6.57822H77.119V7.57822ZM82.703 10.5172L77.4129 6.67371L76.8251 7.48273L82.1152 11.3262L82.703 10.5172ZM80.864 17.295L82.8846 11.0762L81.9336 10.7672L79.9129 16.986L80.864 17.295ZM85.3846 12.8926L80.0946 16.736L80.6824 17.545L85.9724 13.7016L85.3846 12.8926ZM91.2625 16.736L85.9724 12.8926L85.3846 13.7016L90.6747 17.545L91.2625 16.736ZM88.4724 11.0762L90.4931 17.295L91.4441 16.986L89.4235 10.7672L88.4724 11.0762ZM93.9441 6.67371L88.6541 10.5172L89.2419 11.3262L94.5319 7.48273L93.9441 6.67371ZM87.6992 7.57822H94.238V6.57822H87.6992V7.57822Z" fill="white" mask="url(#path-1-inside-1_4485_2902)"/>
                           </svg>
                        </div>
                        <?php if (
                           $product_rating != "" ||
                           $product_total_no_reviews != ""
                           ) { ?>
                        <div class="review-count">
                           <strong class="review-rate"><?php if (
                              $product_rating != ""
                              ) {
                              echo $product_rating;
                              } ?>
                           </strong><?php if (
                              $product_total_no_reviews !=
                              ""
                              ) {
                              echo "(" .
                                  $product_total_no_reviews .
                                  " Reviews)";
                              } ?>
                        </div>
                        <?php } ?>
                     </div>
                  </div>
                  <?php }
                     ?>
                  <div class="banner-block--info <?php if($wr_seo_broker_review != '' && $wr_seo_broker_review != 'na'){ echo 'banner-block--infoReview'; }?> "><?php
                     if($wr_seo_broker_review != '' && $wr_seo_broker_review != 'na'){ ?>
                        <div class="banner-block--info-left">
                              <svg class="icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none">
                                 <path d="M7.29997 8.46662L6.3333 7.51662C6.21108 7.3944 6.0583 7.33329 5.87497 7.33329C5.69163 7.33329 5.5333 7.39996 5.39997 7.53329C5.27775 7.65551 5.21663 7.81107 5.21663 7.99996C5.21663 8.18885 5.27775 8.3444 5.39997 8.46662L6.8333 9.89996C6.96663 10.0333 7.12219 10.1 7.29997 10.1C7.47775 10.1 7.6333 10.0333 7.76663 9.89996L10.6 7.06662C10.7333 6.93329 10.7972 6.77773 10.7916 6.59996C10.7861 6.42218 10.7222 6.26662 10.6 6.13329C10.4666 5.99996 10.3083 5.93051 10.125 5.92496C9.94163 5.9194 9.7833 5.98329 9.64997 6.11662L7.29997 8.46662ZM5.4333 14.5L4.46663 12.8666L2.6333 12.4666C2.46663 12.4333 2.3333 12.3472 2.2333 12.2083C2.1333 12.0694 2.09441 11.9166 2.11663 11.75L2.29997 9.86662L1.04997 8.43329C0.938856 8.31107 0.883301 8.16662 0.883301 7.99996C0.883301 7.83329 0.938856 7.68885 1.04997 7.56662L2.29997 6.13329L2.11663 4.24996C2.09441 4.08329 2.1333 3.93051 2.2333 3.79162C2.3333 3.65273 2.46663 3.56662 2.6333 3.53329L4.46663 3.13329L5.4333 1.49996C5.52219 1.35551 5.64441 1.25829 5.79997 1.20829C5.95552 1.15829 6.11108 1.16662 6.26663 1.23329L7.99997 1.96662L9.7333 1.23329C9.88886 1.16662 10.0444 1.15829 10.2 1.20829C10.3555 1.25829 10.4777 1.35551 10.5666 1.49996L11.5333 3.13329L13.3666 3.53329C13.5333 3.56662 13.6666 3.65273 13.7666 3.79162C13.8666 3.93051 13.9055 4.08329 13.8833 4.24996L13.7 6.13329L14.95 7.56662C15.0611 7.68885 15.1166 7.83329 15.1166 7.99996C15.1166 8.16662 15.0611 8.31107 14.95 8.43329L13.7 9.86662L13.8833 11.75C13.9055 11.9166 13.8666 12.0694 13.7666 12.2083C13.6666 12.3472 13.5333 12.4333 13.3666 12.4666L11.5333 12.8666L10.5666 14.5C10.4777 14.6444 10.3555 14.7416 10.2 14.7916C10.0444 14.8416 9.88886 14.8333 9.7333 14.7666L7.99997 14.0333L6.26663 14.7666C6.11108 14.8333 5.95552 14.8416 5.79997 14.7916C5.64441 14.7416 5.52219 14.6444 5.4333 14.5ZM6.29997 13.3L7.99997 12.5666L9.7333 13.3L10.6666 11.7L12.5 11.2666L12.3333 9.39996L13.5666 7.99996L12.3333 6.56662L12.5 4.69996L10.6666 4.29996L9.69997 2.69996L7.99997 3.43329L6.26663 2.69996L5.3333 4.29996L3.49997 4.69996L3.66663 6.56662L2.4333 7.99996L3.66663 9.39996L3.49997 11.3L5.3333 11.7L6.29997 13.3Z" fill="white"/>
                              </svg>
                                 Reviewed by <a href="<?= get_the_permalink($wr_seo_broker_review); ?>" class="text-white"><strong> <?= get_the_title($wr_seo_broker_review); ?> </strong></a>
                        </div><?php
                      } ?>
                  <div class="banner-block--info-right">
                     <?php
                        if ($modified_date != "") { ?>
                     <div class="page-info--update-date">
                     <svg class="icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3.33333 14.6666C2.96667 14.6666 2.65278 14.536 2.39167 14.2749C2.13056 14.0138 2 13.6999 2 13.3333V3.99992C2 3.63325 2.13056 3.31936 2.39167 3.05825C2.65278 2.79714 2.96667 2.66659 3.33333 2.66659H4V1.33325H5.33333V2.66659H10.6667V1.33325H12V2.66659H12.6667C13.0333 2.66659 13.3472 2.79714 13.6083 3.05825C13.8694 3.31936 14 3.63325 14 3.99992V13.3333C14 13.6999 13.8694 14.0138 13.6083 14.2749C13.3472 14.536 13.0333 14.6666 12.6667 14.6666H3.33333ZM3.33333 13.3333H12.6667V6.66658H3.33333V13.3333ZM3.33333 5.33325H12.6667V3.99992H3.33333V5.33325ZM8 9.33325C7.81111 9.33325 7.65278 9.26936 7.525 9.14158C7.39722 9.01381 7.33333 8.85547 7.33333 8.66658C7.33333 8.4777 7.39722 8.31936 7.525 8.19159C7.65278 8.06381 7.81111 7.99992 8 7.99992C8.18889 7.99992 8.34722 8.06381 8.475 8.19159C8.60278 8.31936 8.66667 8.4777 8.66667 8.66658C8.66667 8.85547 8.60278 9.01381 8.475 9.14158C8.34722 9.26936 8.18889 9.33325 8 9.33325ZM5.33333 9.33325C5.14444 9.33325 4.98611 9.26936 4.85833 9.14158C4.73056 9.01381 4.66667 8.85547 4.66667 8.66658C4.66667 8.4777 4.73056 8.31936 4.85833 8.19159C4.98611 8.06381 5.14444 7.99992 5.33333 7.99992C5.52222 7.99992 5.68056 8.06381 5.80833 8.19159C5.93611 8.31936 6 8.4777 6 8.66658C6 8.85547 5.93611 9.01381 5.80833 9.14158C5.68056 9.26936 5.52222 9.33325 5.33333 9.33325ZM10.6667 9.33325C10.4778 9.33325 10.3194 9.26936 10.1917 9.14158C10.0639 9.01381 10 8.85547 10 8.66658C10 8.4777 10.0639 8.31936 10.1917 8.19159C10.3194 8.06381 10.4778 7.99992 10.6667 7.99992C10.8556 7.99992 11.0139 8.06381 11.1417 8.19159C11.2694 8.31936 11.3333 8.4777 11.3333 8.66658C11.3333 8.85547 11.2694 9.01381 11.1417 9.14158C11.0139 9.26936 10.8556 9.33325 10.6667 9.33325ZM8 11.9999C7.81111 11.9999 7.65278 11.936 7.525 11.8083C7.39722 11.6805 7.33333 11.5221 7.33333 11.3333C7.33333 11.1444 7.39722 10.986 7.525 10.8583C7.65278 10.7305 7.81111 10.6666 8 10.6666C8.18889 10.6666 8.34722 10.7305 8.475 10.8583C8.60278 10.986 8.66667 11.1444 8.66667 11.3333C8.66667 11.5221 8.60278 11.6805 8.475 11.8083C8.34722 11.936 8.18889 11.9999 8 11.9999ZM5.33333 11.9999C5.14444 11.9999 4.98611 11.936 4.85833 11.8083C4.73056 11.6805 4.66667 11.5221 4.66667 11.3333C4.66667 11.1444 4.73056 10.986 4.85833 10.8583C4.98611 10.7305 5.14444 10.6666 5.33333 10.6666C5.52222 10.6666 5.68056 10.7305 5.80833 10.8583C5.93611 10.986 6 11.1444 6 11.3333C6 11.5221 5.93611 11.6805 5.80833 11.8083C5.68056 11.936 5.52222 11.9999 5.33333 11.9999ZM10.6667 11.9999C10.4778 11.9999 10.3194 11.936 10.1917 11.8083C10.0639 11.6805 10 11.5221 10 11.3333C10 11.1444 10.0639 10.986 10.1917 10.8583C10.3194 10.7305 10.4778 10.6666 10.6667 10.6666C10.8556 10.6666 11.0139 10.7305 11.1417 10.8583C11.2694 10.986 11.3333 11.1444 11.3333 11.3333C11.3333 11.5221 11.2694 11.6805 11.1417 11.8083C11.0139 11.936 10.8556 11.9999 10.6667 11.9999Z" fill="white"/>
                    </svg>

                        Updated: <?= $modified_date ?>
                     </div>
                     <?php }
                        if ($read_time != "") { ?>
                     <div class="page-info--read-time">
                     <svg class="icon" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5.33317 13.3333H10.6665V11.3333C10.6665 10.5999 10.4054 9.97214 9.88317 9.44992C9.36095 8.9277 8.73317 8.66658 7.99984 8.66658C7.2665 8.66658 6.63873 8.9277 6.1165 9.44992C5.59428 9.97214 5.33317 10.5999 5.33317 11.3333V13.3333ZM7.99984 7.33325C8.73317 7.33325 9.36095 7.07214 9.88317 6.54992C10.4054 6.0277 10.6665 5.39992 10.6665 4.66658V2.66659H5.33317V4.66658C5.33317 5.39992 5.59428 6.0277 6.1165 6.54992C6.63873 7.07214 7.2665 7.33325 7.99984 7.33325ZM2.6665 14.6666V13.3333H3.99984V11.3333C3.99984 10.6555 4.15817 10.0194 4.47484 9.42492C4.7915 8.83047 5.23317 8.35547 5.79984 7.99992C5.23317 7.64436 4.7915 7.16936 4.47484 6.57492C4.15817 5.98047 3.99984 5.34436 3.99984 4.66658V2.66659H2.6665V1.33325H13.3332V2.66659H11.9998V4.66658C11.9998 5.34436 11.8415 5.98047 11.5248 6.57492C11.2082 7.16936 10.7665 7.64436 10.1998 7.99992C10.7665 8.35547 11.2082 8.83047 11.5248 9.42492C11.8415 10.0194 11.9998 10.6555 11.9998 11.3333V13.3333H13.3332V14.6666H2.6665Z" fill="white"/>
                     </svg>
                        <?= $read_time ?> mins read
                     </div>
                     <?php }
                        ?>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</section>