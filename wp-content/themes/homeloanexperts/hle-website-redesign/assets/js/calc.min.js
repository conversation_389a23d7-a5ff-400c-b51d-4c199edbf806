/* =========================================================
   Helpers (deduped)
   ========================================================= */
function currentPath() {
  return location.pathname.replace(/\/$/, '');
}

// Excel-like PMT helper (returns positive payment)
function pmt(rate, nper, pv, fv, type) {
  rate = Number(rate); nper = Number(nper); pv = Number(pv);
  fv = Number(fv || 0); type = Number(type || 0);
  if (rate === 0) return Math.abs(((pv + fv) / nper).toFixed(2));
  var pvif = Math.pow(1 + rate, nper);
  var p = (rate * (pv * pvif + fv)) / ((type ? 1 + rate : 1) * (pvif - 1));
  return Math.abs(p.toFixed(2));
}

function pmt2(rate, nperiod, pv, fv, type) {
  if (!fv) fv = 0;
  if (!type) type = 0;
  if (rate == 0) return -(pv + fv) / nperiod;
  var pvif = Math.pow(1 + rate, nperiod);
  var p = rate / (pvif - 1) * -(pv * pvif + fv);
  if (type == 1) p /= (1 + rate);
  return p;
}

function numberWithCommas(a) {
  var b = String(a).split(".");
  b[0] = b[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  return b.join(".");
}

var myHem = [];
myHem[0] = [1100, 1400, 1800, 1900, 2100, 2200];
myHem[1] = [2000, 2200, 2700, 3000, 3200, 3500];
myHem[2] = [3100, 3300, 3800, 4100, 4300, 4600];

function conv_number(expr, decplaces) {
  var str = "" + Math.round(eval(expr) * Math.pow(10, decplaces));
  while (str.length <= decplaces) str = "0" + str;
  var decpoint = str.length - decplaces;
  return (str.substring(0, decpoint) + "." + str.substring(decpoint, str.length));
}

function pv(c, a, b, d) {
  c = parseFloat(c); a = parseFloat(a); b = parseFloat(b); d = parseFloat(d);
  if (a == 0) { alert("Why do you want to test me with zeros?"); return 0; }
  if (c == 0) { pv_value = -(d + (b * a)); }
  else {
    x = Math.pow(1 + c, -a);
    y = Math.pow(1 + c, a);
    pv_value = -(x * (d * c - b + y * b)) / c;
  }
  pv_value = (pv_value, 2);
  return pv_value;
}

function fv(c, a, b, d) {
  c = parseFloat(c); a = parseFloat(a); b = parseFloat(b); d = parseFloat(d);
  if (a == 0) { alert("Why do you want to test me with zeros?"); return 0; }
  if (c == 0) { fv_value = -(d + (b * a)); }
  else {
    x = Math.pow(1 + c, a);
    fv_value = -(-b + x * b + c * x * d) / c;
  }
  fv_value = conv_number(fv_value, 2);
  return fv_value;
}

/* =========================================================
   Equity Calculator (run only if .equity-calc exists)
   ========================================================= */
jQuery(function ($) {
  jQuery(".num-val").on("change",function(e){
    e.which>=37&&e.which<=40||jQuery(this).val(function(e,r){return numberWithCommas(r)})
  });

  var e=jQuery("#int-rate").val().replace("%","");
  e=Number(e)/100;
  var r=jQuery("#comp-rate").val().replace("%","");
  r=Number(r),
  jQuery("#new-calc-btn").click(function(){
    var t=jQuery("#prop-value").val().replace(/,/g,""),
        n=jQuery("#loan-amt").val().replace(/,/g,"");
    if(0===t.length||0===n.length)
      jQuery("#NewResult").removeClass("hidden"),
      jQuery("#result-row").addClass("hidden"),
      jQuery("p.CalcNote").text("Please make sure to have filled out all the input fields.");
    else{
      var a=parseFloat(t).toFixed(2),
          u=parseFloat(n).toFixed(2),
          s=e/12,
          o=u/a*100,
          l=a-u,
          i=(.9*a-u).toFixed(2);
      if(o<90){
        jQuery("#NewResult").addClass("hidden"),
        jQuery("#result-row").removeClass("hidden");
        var y=pmt(s,360,u,0,0);
        jQuery("#res-comment").html("<li class='tick-list'>You can refinance and increase your home loan up to 90% of the value of your property in order to release <strong><span id='nw-funds2'></span> in equity</strong>.</li><li class='tick-list'>Our lowest home loan rate is <strong> "+(100*e).toFixed(2)+"%</strong> (comparison rate <strong> "+r+"% </strong>), which means that you can refinance your loan and your monthly repayments would drop to <strong><span id='nw-rep'></span></strong> per month over 30 years.</li> <li class='tick-list'>Learn how to use a property you already own to buy your next property, even without a deposit.<a href='/homebuyers-institute/?utm_source=website&utm_medium=calculators&utm_campaign=HBI' target='_blank'> Take our Free Home Buyers Course.</a> Free for a limited period.</li>"),
        jQuery("#equi-amt").text("$"+l),
        jQuery("#nw-rep").text("$"+y)
      } else
        jQuery("#NewResult").addClass("hidden"),
        jQuery("#result-row").removeClass("hidden"),
        jQuery("#res-comment").html("<li class='cross-list'>Unfortunately you cannot access your equity at this time unless you sell your property.</li> <li class='tick-list'>Learn how to use a property you already own to buy your next property, even without a deposit.<a href='/homebuyers-institute/?utm_source=website&utm_medium=calculators&utm_campaign=HBI' target='_blank'> Take our Free Home Buyers Course.</a> Free for a limited period.</li>"),
        jQuery("#equi-amt").text("$"+l);

      jQuery("#nw-funds, #nw-funds2").text("$"+Number(i)),
      jQuery("#equi-amt,#nw-funds,#nw-rep,#nw-funds2").each(function(){
        var e=jQuery(this).text(), r=numberWithCommas(e);
        jQuery(this).text(r)
      }),
      jQuery("#qualify").text("RE-CALCULATE")
    }
  });
});



/* =========================================================
   PI vs IO Calculator — run only on this exact page
   ========================================================= */
jQuery(function(){
  jQuery("#qualify").click(function () {
    if (jQuery("#io-rate").val().length <= 0 || jQuery("#loan-amt").val().length <= 0 || jQuery("#tax-rate").val().length <= 0 || jQuery("#pi-rate").val().length <= 0) {
      jQuery("#NewCalcNotice").removeClass("hidden");
      jQuery("p.CalcNote").text("Please make sure to have filled out all the input fields.");
    } else {
      var g = parseFloat(jQuery("#io-rate").val()) / 100;
      var k = parseFloat(jQuery("#pi-rate").val()) / 100;
      var i = parseFloat(jQuery("#tax-rate").val()) / 100;
      var m = parseFloat(jQuery("#loan-amt").val()) * 100;
      var e = parseInt(m);
      var d = jQuery("#inv-loan").val();
      var c = (e / 100) * g;
      var j = pmt(k / 12, 360, e / 100, 0, 0) * 12;
      var b = k * (e / 100);

      jQuery("#NewCalcNotice").addClass("hidden");
      jQuery("#result-row").removeClass("hidden");

      jQuery("#io-repayment, #io-int-exp").text("$" + c.toFixed(2));
      jQuery("#pi-repayment").text("$" + j.toFixed(2));
      jQuery("#pi-int-exp").text("$" + b.toFixed(2));

      var h = j - c;      // PI vs IO cashflow
      var n = c - b;      // extra interest cost of IO vs PI
      var r = (d === "yes") ? n * i : 0; // tax refund
      var a = n - r;      // after-tax cost
      var q = h + r;      // after-tax cashflow advantage
      var l = ((a / q) * 100).toFixed(2);

      var f = (e / 100) * g * 5 + pmt(k / 12, 300, e / 100, 0, 0) * 300 - e / 100;
      var o = (j / 12) * 360 - e / 100;
      var p = f - o;

      jQuery("#pi-cshflw-res").text("$" + h.toFixed(2));
      jQuery("#io-int-exp-res").text("$" + n.toFixed(2));
      jQuery("#htax-rfund-res").text("$" + r.toFixed(2));
      jQuery("#atax-cost-res").text("$" + a.toFixed(2));
      jQuery("#atax-cshflw-res").text("$" + q.toFixed(2));
      jQuery("#eff-roi-res").text(l + "%");
      jQuery("#ResCont").addClass("alert-success");
      jQuery("#io_int_cost").text("$" + f.toFixed(2));
      jQuery("#pi_int_cost").text("$" + o.toFixed(2));
      jQuery("#pi_io_savings").text("$" + p.toFixed(2));

      jQuery("#io-repayment, #io-int-exp, #pi-int-exp, #pi-repayment, #pi-cshflw-res, #io-int-exp-res, #htax-rfund-res, #atax-cost-res, #atax-cshflw-res, #io_int_cost, #pi_int_cost, #pi_io_savings").each(function () {
        var t = jQuery(this).text();
        var s = numberWithCommas(t);
        jQuery(this).text(s);
      });
    }
  });
});

