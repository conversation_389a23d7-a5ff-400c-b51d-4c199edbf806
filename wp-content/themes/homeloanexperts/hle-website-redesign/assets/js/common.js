jQuery(document).ready(function (jQuery) {

	//Free Quote Guarantors Page Force Link Footer Free Quote
    const freeQuoteGuarantorPages = data.pageID;
    [327, 30605, 30601, 30610, 31108, 30595, 35272, 31265, 64776, 56501].includes(Number(freeQuoteGuarantorPages)) && jQuery("#menu-item-82426 a").attr("href", "/free-quote-guarantor-loan/");


    var hasAdminBanner = jQuery("body").hasClass("logged-in admin-bar");
    var adminbarHeight = jQuery("#wpadminbar").height();
    var topBarBannerHeight = jQuery("#topbar-section").height();
    var topBarBanner = jQuery("#topbar-section");
    var hasTopBanner = jQuery("#topbar-section").length > 0;
    var IsMobileDevice = jQuery(window).width() < 1200;
    if (jQuery("#topbar-section").length > 0) {
        if (hasAdminBanner) {
            jQuery("#topbar-section").css({ top: adminbarHeight });
        }
        jQuery("body").css({ paddingTop: topBarBannerHeight });
    }
    jQuery(".top__banner-bar svg").click(function () {
        jQuery("#topbar-section").slideUp("fast").remove();
        jQuery("body").css({ paddingTop: 0 });
        jQuery("header.header").css({ top: 0 });
    });
    function toggleSearchbox() {
        var isSearchBoxOpen = !1;
        if (IsMobileDevice) {
            isSearchBoxOpen = !0;
            jQuery(".search-wrap").addClass("open");
        }
        jQuery("#btnSearch").click(function (e) {
            if (isSearchBoxOpen && jQuery(".search-wrap").hasClass("open")) {
                var query = jQuery("#menuSearch").val();
                window.location.href = "https://www.homeloanexperts.com.au/search/?q=" + query;
            } else {
                var headerHeight = jQuery("header").outerHeight();
                var bodyHeight = jQuery("body").outerHeight();
                var SearchOverlay = jQuery("<div class='header-overlay'></div>");
                var hasAdminBanner = jQuery("body").hasClass("logged-in admin-bar");
                var adminbarHeight = jQuery("#wpadminbar").height();
                if (hasAdminBanner) {
                    headerHeight = headerHeight + adminbarHeight;
                }
                jQuery(".search-wrap").toggleClass("open");
                jQuery(".searchbox-menu").toggleClass("open");
                jQuery(".navbar-cta-grp").toggleClass("hide").fadeOut("slow");
                jQuery("#menuSearch").focus();
                jQuery("body").append(SearchOverlay);
                hasTopBanner
                    ? jQuery(SearchOverlay)
                          .css({ top: headerHeight + topBarBannerHeight, height: bodyHeight })
                          .addClass("show")
                    : jQuery(SearchOverlay).css({ top: headerHeight, height: bodyHeight }).addClass("show");
                isSearchBoxOpen = !0;
            }
        });
    }
    jQuery("body").click(function () {
        var IsSearchBoxOpen = jQuery(".search-wrap").hasClass("open") | jQuery(".searchbox-menu").hasClass("open");
        var SearchOverlay = jQuery(".header-overlay");
        if (IsSearchBoxOpen) {
            if (!IsMobileDevice) {
                jQuery(".search-wrap").removeClass("open");
            }
            jQuery(".searchbox-menu").removeClass("open");
            jQuery(".navbar-cta-grp").removeClass("hide");
            SearchOverlay.remove();
        }
    });
    jQuery(".search-wrap").click(function (e) {
        e.stopPropagation();
    });
    jQuery(".modal-content").click(function (e) {
        e.stopPropagation();
    });
    function menuHover() {
        var headerHeight = jQuery("header").outerHeight();
        var bodyHeight = jQuery("body").outerHeight();
        var headerOverlay = jQuery("<div class='header-overlay menu-header-overlay'></div>");
        var hasAdminBanner = jQuery("body").hasClass("logged-in admin-bar");
        var adminbarHeight = jQuery("#wpadminbar").height();
        if (hasAdminBanner) {
            headerHeight = headerHeight + adminbarHeight;
        }
        if (jQuery("body .header-overlay").length === 0) {
            jQuery(headerOverlay).css({ top: headerHeight, height: bodyHeight }).addClass("show");
        }
    }
    jQuery(".header .navbar-item.menu-item-has-children").hover(
        function (e) {
            if (jQuery(window).width() > 1200) {
                menuHover();
                jQuery(this).addClass("open");
            } else {
            }
        },
        function () {
            if (jQuery(window).width() > 1200) {
                jQuery(this).children(".sub-menu").slideUp();
                jQuery(".menu-header-overlay").remove();
                jQuery(this).removeClass("open");
            }
        }
    );
    function toggleFooterMenu() {
        if (jQuery(window).width() < 768) {
            jQuery(".footer-menu-list").hide();
            jQuery(".footer-menu--title")
                .unbind("click")
                .click(function () {
                    jQuery(this).siblings(".footer-menu-list").slideToggle();
                    jQuery(this).toggleClass("show");
                });
        } else {
            jQuery(".footer-menu-list").show();
            jQuery(".footer-menu--title").unbind("click");
        }
    }
    function hamburgerMenu() {
        jQuery("#navHamburger")
            .unbind("click")
            .click(function (e) {
                var headerHeight = jQuery("header").outerHeight();
                var topBarBannerHeight = jQuery("#topbar-section").height();
                e.preventDefault();
                if (IsMobileDevice) {
                    jQuery(".navbar-collapse").toggleClass("show navbar-collapse--menu-mb");
                    jQuery("body").toggleClass("menu-active");
                    jQuery(".hle-nav__search").toggleClass("move-inside");
                    jQuery(this).toggleClass("close");
                    jQuery(".navbar-collapse--menu-mb").css({ top: headerHeight });
                    if (jQuery("#navHamburger").hasClass("close")) {
                        topBarBanner.slideUp("fast");
                        jQuery("body").css({ paddingTop: 0, overflow: "hidden", position: "fixed", width: "100%" });
                        jQuery("header.header").css({ top: 0 });
                    } else {
                        jQuery("body").removeAttr("style");
                        topBarBanner.slideDown("fast");
                        jQuery("body").css({ paddingTop: topBarBannerHeight });
                        jQuery("header.header").css({ top: topBarBannerHeight });
                    }
                } else {
                    jQuery(".hle-nav__search").removeClass("move-inside");
                }
            });
    }
    function toggleNavMenu(e) {
        if (jQuery(window).width() < 1200) {
            jQuery(document).on("click", ".navbar-item > a", function (e) {
                if (e.target == this) {
                    e.preventDefault();
                    jQuery(".sub-menu").not(jQuery(this).siblings(".navbar-collapse--menu-mb .sub-menu")).removeClass("open");
                    jQuery(".navbar-collapse--menu-mb .sub-menu").removeAttr("style");
                    jQuery(this).siblings(".sub-menu").toggleClass("open");
                }
            });
        } else {
            jQuery(".navbar-collapse--menu-mb .navbar-item").unbind("click").removeClass("open");
        }
    }
    hamburgerMenu();
    toggleSearchbox();
    toggleNavMenu();
    jQuery(window).resize(function () {
        toggleFooterMenu();
        hamburgerMenu();
        if (!IsMobileDevice) {
            jQuery(".navbar-collapse").removeClass("show navbar-collapse--menu-mb");
        }
    });
    function init() {
        var lastScrollPos = window.scrollY;
        window.addEventListener("scroll", function pageIsScrolled() {
            var hasTopBanner = jQuery("#topbar-section").length > 0;
            var hasAdminBanner = jQuery("body").hasClass("logged-in admin-bar");
            var adminbarHeight = jQuery("#wpadminbar").height();
            var topBarBannerHeight = jQuery("#topbar-section").height();
            var distanceY = window.scrollY || document.documentElement.scrollTop,
                shrinkOn = jQuery("header.header").outerHeight();
            if (distanceY > 0) {
                jQuery("header.header").addClass("header-sticky");
                hasTopBanner ? jQuery("#topbar-section").css({ top: adminbarHeight, "z-index": "1064" }) : "";
                if (hasAdminBanner) {
                    jQuery("header.header").css({ top: adminbarHeight });
                    jQuery("body").css({ paddingTop: adminbarHeight });
                    if (hasTopBanner) {
                        jQuery("header.header").css({ top: adminbarHeight + topBarBannerHeight });
                    }
                } else if (!hasTopBanner) {
                    jQuery("header.header").css({ top: 0 });
                    jQuery("body").css({ paddingTop: 0 });
                    hasAdminBanner ? jQuery("header.header").css({ top: adminbarHeight }) : jQuery("header.header").css({ top: 0 });
                } else {
                    jQuery("header.header").css({ top: topBarBannerHeight });
                    jQuery("body").css({ "padding-top": 0 });
                }
            } else {
                if (jQuery("header.header").hasClass("header-sticky")) {
                    jQuery("header.header").removeClass("header-sticky");
                    if (hasTopBanner) {
                        if (jQuery("body").hasClass("menu-active")) {
                            jQuery("body").css({ paddingTop: 0 });
                        } else {
                            jQuery("body").css({ paddingTop: topBarBannerHeight });
                        }
                    }
                    if (hasAdminBanner) {
                        jQuery("body").css({ "padding-top": adminbarHeight });
                    }
                }
            }
        });
    }
    window.onload = init();
    setTimeout(function () {
        var hasChatbot = jQuery("#chat-box-holder").length > 0;
        var heightOfChatbot = jQuery(".chat-box").height();
        if (hasChatbot) {
            jQuery(".footer-bottom").css({ paddingBottom: heightOfChatbot });
            jQuery("body").addClass("has--chatbot");
        }
    }, 8000);
    var btnToScollTop = jQuery("#scrollTopButton");
    jQuery(window).scroll(function () {
        if (jQuery(window).scrollTop() > 300) {
            btnToScollTop.addClass("show");
        } else {
            btnToScollTop.removeClass("show");
        }
    });
    btnToScollTop.on("click", function (e) {
        e.preventDefault();
        jQuery("html, body").animate({ scrollTop: 0 }, "slow");
    });
});
