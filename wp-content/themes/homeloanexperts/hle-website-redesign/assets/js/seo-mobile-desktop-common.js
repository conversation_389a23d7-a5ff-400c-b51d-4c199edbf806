function isElementInViewportVertically(e) {
    if (e) {
        var t = e.getBoundingClientRect();
        return t.top < window.innerHeight && t.bottom >= 0;
    }
}
function updateStickySidebar() {
    isElementInViewportVertically(document.getElementById("faq-section")) ? (jQuery("#sidebar").addClass("fixed"), jQuery("body").addClass("sticky")) : (jQuery("#sidebar").removeClass("fixed"), jQuery("body").removeClass("sticky"));
}
jQuery(document).ready(function (e) {
    //Free Quote Static Page Link Force Link Footer Free Quote 
    const t = data.pageID;
    [13269, 2435, 13404, 63666, 71307, 70491, 290, 55783, 84408].includes(Number(t)) && jQuery("#menu-item-82426 a").attr("href", "/free-quote-static/");

    //Free Quote Doctors Page Force Link Footer Free Quote
    const freeQuoteDocPages = data.pageID;
    [28378, 60864, 59497, 21507, 60301, 23243, 71614, 20512, 20502, 45324, 23249].includes(Number(freeQuoteDocPages)) && jQuery("#menu-item-82426 a").attr("href", "/free-quote-doctor/");


    //Free Quote Doctors Page Force Link Footer Free Quote
    const freeQuoteGuarantorPages = data.pageID;
    [327, 30605, 30601, 30610, 31108, 30595, 35272, 31265, 64776, 56501].includes(Number(freeQuoteGuarantorPages)) && jQuery("#menu-item-82426 a").attr("href", "/free-quote-guarantor-loan/");

        jQuery(".faq__accordion--tab").click(function (e) {
            jQuery(".faq__accordion--body").is(":visible") && jQuery(".faq__accordion--body").slideUp(300);
            var t = jQuery(this).closest(".faq__accordion--tab").find(".faq__accordion--body");
            t.is(":visible") ? (t.slideUp(300), jQuery(this).removeClass("openTab")) : (t.slideDown(300), jQuery(".faq__accordion--tab").removeClass("openTab"), jQuery(this).addClass("openTab"));
        }),
        jQuery(".tabs__pill a").on("click", function (e) {
            var t = jQuery(this).data("tab");
            jQuery(".tabs__content").removeClass("active"), jQuery("#" + t).addClass("active"), jQuery(".tabs__pill a").attr("aria-selected", "false"), jQuery(this).attr("aria-selected", "true"), e.preventDefault();
        }),
        window.addEventListener("scroll", updateStickySidebar),
        updateStickySidebar(),
        setTimeout(function () {
            var e = jQuery("#chat-box-holder").length > 0,
                t = jQuery(".chat-box").height();
            e && (jQuery(".footer-bottom").css({ paddingBottom: t }), jQuery("body").addClass("has--chatbot"));
        }, 8e3);
    var a = jQuery("#scrollTopButton");
    jQuery(window).scroll(function () {
        jQuery(window).scrollTop() > 300 ? a.addClass("show") : a.removeClass("show");
    }),
        a.on("click", function (e) {
            jQuery("html, body").animate({ scrollTop: 0 }, "slow");
        }),
        jQuery(".video_testimonial-btn").click(function (e) {
            e.preventDefault(),
                jQuery(".video_testimonial-video").append(
                    '<iframe width="400" height="400" src="https://www.youtube.com/embed/Yfy5XGMHuvQ?autoplay=1" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen></iframe>'
                ),
                jQuery(this).hide();
        }),
        jQuery(".highlight-toc--list").each(function () {
            var e = jQuery(this).find("li");
            e.length > 6 && (jQuery(".highlight-toc--btn").show(), e.slice(6).hide().addClass("toggleable"), jQuery(".highlight-toc--btn .text").text("View More")),
                jQuery(".highlight-toc--btn").on("click", function (t) {
                    t.preventDefault(), e.filter(".toggleable").slideToggle();
                    var a = jQuery(this).hasClass("expanded");
                    jQuery(this)
                        .find(".text")
                        .text(a ? "View More" : "View Less"),
                        jQuery(this).toggleClass("expanded");
                });
        }),
        jQuery(".collapsible-toggle-button").on("click", function () {
            jQuery(this).parent(".hle-collapsible-container").toggleClass("expanded");
        }),
        jQuery(".btn--summary").on("click", function () {
            jQuery("#summary-popup").addClass("active");
        }),
        jQuery("#summary-close--button").on("click", function (e) {
            jQuery("#summary-popup").removeClass("active");
        }),
        jQuery(".btn--summary").click(function () {
            "none" != jQuery(".skeleton-line").css("display")
                ? jQuery.ajax({
                      type: "post",
                      url: data.ajax_url,
                      data: { action: "ai_summary_content_compiler", pageID: data.pageID, nonce: data.token },
                      beforeSend: function () {},
                      error: function (e) {
                          console.log(e);
                      },
                      success: function (e) {
                          jQuery(".summary-popup--result").append(e);
                      },
                      complete: function (e) {
                          jQuery(".skeleton-line").css({ display: "none" }), jQuery(".skeleton-wrapper").addClass("hide-skeleton");
                      },
                  })
                : jQuery(".skeleton-line").css({ display: "none" });
        });
        
});

// Check if the device supports touch input (i.e., it's a mobile or tablet)
const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

if (isTouchDevice) {
  // Select key DOM elements
  const trigger = document.querySelector('.navbar-cta--expert'); // The button that opens the dropdown
  const dropdown = document.querySelector('.navbar-cta__dropdown'); // The dropdown content
  const closeIcon = document.querySelector('.navbar-cta__close-icon'); // The "X" icon to close the dropdown
  const main = document.querySelector('.main'); // Main container, used for adding overlay effect
  const body = document.body; // Body element, used to prevent scroll when dropdown is open

  // Ensure all elements exist before attaching event listeners
  if (trigger && dropdown && closeIcon) {
    
    // On tapping the trigger button, open the dropdown
    trigger.addEventListener('click', (e) => {
      e.preventDefault();        // Prevent default link behavior if any
      e.stopPropagation();       // Stop the event from bubbling up to parent elements
      dropdown.classList.add('active');     // Show the dropdown
      main.classList.add('overlay');        // Add background overlay (e.g., dim effect)
      body.classList.add('no-scroll');      // Disable page scroll
    });

    // On tapping the close (X) icon, close the dropdown
    closeIcon.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropdown.classList.remove('active');  // Hide the dropdown
      main.classList.remove('overlay');     // Remove overlay effect
      body.classList.remove('no-scroll');   // Re-enable page scroll
    });
  }
}
