#incontent-CTA {
  &.internal-calc {
    &.calc-container {
      border: 1px solid #27ade7;
      border-radius: 5px;
      padding: 15px 15px 0 15px;
      -webkit-box-shadow: 5px 6px 10px rgba(0, 0, 0, 0.3);
      box-shadow: 5px 6px 10px rgba(0, 0, 0, 0.3);
      margin-bottom: 15px;
      label {
        font-weight: 400;
        display: inline-block;
        margin-right: 5px;
        margin-top: 10px;
      }
      .input-group {
        position: relative;
        display: table;
        border-collapse: separate;
        margin-top: 5px;
        margin-bottom: 20px;
        &-right{
          .input-group-addon{
            border-radius: 0 4px 4px 0!important;
          }
          .form-control{
            border-radius: 4px 0 0 4px!important;
          }
        }
        .form-control {
          position: relative;
          z-index: 2;
          float: left;
          width: 100%;
          margin-bottom: 0;
          border: 1px solid #ccc;
          height: 45px;
          padding: 6px 12px;
          font-size: 14px;
          border-radius: 0 4px 4px 0;
        }
        .input-group-addon {
          padding: 6px 12px;
          font-size: 1.5rem;
          font-weight: 400;
          line-height: 1;
          text-align: center;
          border: 1px solid #ced4da;
          border-radius: 4px 0 0 4px;
          background-color: #27ade7;
          color: #fff;
          border-color: #27ade7;
          width: 1%;
          white-space: nowrap;
          vertical-align: middle;
          display: table-cell;
          
        }
      }
      .btn {
        border: 1px solid transparent;
        white-space: pre-wrap;
        padding: 15px 20px;
        width: 100%;
        color: #fff;
        font-size: 14px;
        border-radius: 100px;
        text-transform: uppercase;
        display: inline-block;
      }
      #NewResult {
        color: #a94442;
        background-color: #f2dede;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
        padding: 10px;
        margin-bottom: 0;
        p {
          font-size: 1.4rem;
          margin: 0;
          padding: 0;
          color: #b80000;
        }
      }
      #result-row {
        background-color: #d9edf7;
        padding: 15px;
        border-radius: 5px;
        padding-top: 10px;
        h3 {
          text-transform: uppercase;
          color: #27ade7;
          font-size: 14px;
          font-weight: 700;
          margin-top: 10px;
          margin-bottom: 5px;
        }
        ul {
          margin-top: 0;
        }
        p,
        ul {
          font-size: 1.5rem;
        }
      }
      span.pos-res {
        color: #28ad2b;
      }
      .totalRes {
        color: #27ade7;
        font-size: 30px;
        font-weight: bolder;
        line-height: 1.5;
        display: block;
      }
    }
  }
  &.iopi-calc{
    &.calc-container{
      padding: 0;
    }
    .nav-wrap {
      padding: 15px 15px 0 15px;
    }
    select.form-control{
      width: 100%;
      margin-top: 5px;
    }
    label{
      a{
        font-size: 1.4rem;
      }
    }
  }
}
