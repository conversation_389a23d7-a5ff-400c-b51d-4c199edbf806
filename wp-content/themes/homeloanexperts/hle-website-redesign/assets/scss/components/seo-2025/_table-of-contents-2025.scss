.tbc {
    &__card { 
        position: sticky;
        top: 140px; 
        z-index: 10; 

        @include media-breakpoint-down(md) {
         position: static; 
           margin-bottom: 2rem;
        }
    }

    &__heading {
        background-color: $text-color-light;
        font-size: 2.4rem;
        color: #fff;
        border-radius: 12px 12px 0px 0px;
        padding: 1.2rem 2rem;
        font-weight: 700;
        display: flex;
        justify-content: space-between;
        align-items: center;
        @include media-breakpoint-down(md) {
            border-radius: 12px 12px;   
            font-size: 1.6rem;
        }
        &.expanded {
            border-radius: 12px 12px 0px 0px;
            .tbc__toggle-btn{
                svg{
                    transform: rotate(180deg);
                }
            }
        }
        .tbc__toggle-btn {
         
            border: none;
            color: #fff;
            font-size: 1.2rem;
            cursor: pointer;
            display: none;
            padding: 4px 8px;
            align-items: center;
            gap: 2px;
            border-radius: 52px;
            border: 1px solid #FFF;
            background: rgba(255, 255, 255, 0.20);
            @include media-breakpoint-down(md) {
                display: inline-flex;
            }
        }
    }

    &__body {
        padding: 2rem;
        background-color: $primary-light-blue;
        border-radius: 0px 0px 12px 12px;
        font-size: 1.6rem;
        line-height: 1.5;
        margin-bottom: 2rem;
        display: block; 
        transition: all 0.3s ease-in-out;

        @include media-breakpoint-down(md) {
            display: none; 
        }

        ul {
            margin: 0;
            padding: 0;
            list-style: none;

            li {
                margin-bottom: 1rem;
                font-size: 1.4rem;
                line-height: 1.5;

                @include media-breakpoint-up(md) {
                    font-size: 1.6rem;
                }

                a {
                    color: $text-color;
                    text-decoration: none;
                    transition: all 0.3s ease-in-out;

                    &:hover, &.active {
                        color: $secondary;
                        font-weight: 700;
                    }
                }
            }
        }
    }
}