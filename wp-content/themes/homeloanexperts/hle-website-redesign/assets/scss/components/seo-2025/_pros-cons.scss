.pros-cons {
  &__wrapper {
    display: flex;
    gap: 2.6rem;
    margin-bottom: 3rem;
    @include media-breakpoint-down(md) {
      margin-bottom: 2rem;
    }
    > div {
      flex: 0 1 48%;
      max-width: 50%;
        @include media-breakpoint-down(md) {
            max-width: unset;
                gap: 2rem;
        }
    }
    @include media-breakpoint-down(md) {
        flex-direction: column;
    }
  }
  &__header{
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }
  &__item {
    padding: 2rem;
    border-radius: 1.2rem;
    background: rgba(41, 173, 44, 0.05);
    &--pros {
      background: rgba(41, 173, 44, 0.05);
    }
    &--cons {
      background: rgba(157, 42, 42, 0.05);
    }
    > ul{
        &:last-of-type{
            margin-bottom: 0;
        }
    }
  }
}
