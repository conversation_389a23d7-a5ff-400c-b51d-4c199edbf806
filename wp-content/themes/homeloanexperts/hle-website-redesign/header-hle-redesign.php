<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Set Static Cache Life Span to 1 year i.e-- 31536000 secs !-->
    <meta http-equiv="Cache-Control" content="max-age=31536000, public">
    
    <!-- Meta  --><?php
    global $post;
    $yoast_wpseo_title = get_post_meta(get_the_ID(), '_yoast_wpseo_title', true);  ?>

    <title><?php echo ($yoast_wpseo_title!= '') ? $yoast_wpseo_title : get_the_title(); ?></title><?php

    /**
     * VWO code on custom field with Shortcode
    */
    $VWO_code = !empty($post) ? do_shortcode ( get_post_meta( $post->ID, 'VWO_code', true )):'';
    $VWO_code = isset($VWO_code) && $VWO_code != '' ? $VWO_code : '';
    echo $VWO_code;

    /**
     * Exclude Prefetching Free Quote for URL's under mortgage-calculator as parent page 
    */
    if (!(strpos($_SERVER['REQUEST_URI'], 'mortgage-calculators') !== false)) { ?>
        <link rel="prefetch" href="<?php echo home_url('/free-quote/'); ?>"><?php

    }
    wp_head();  

    $containerId = environment_check(); 

    // Hotjar code on custom field with Shortcodes 
      $hotjar_code = !empty($post) ? do_shortcode ( get_post_meta( $post->ID, 'hotjar_code', true )):'';
      $hotjar_code = isset($hotjar_code) && $hotjar_code != '' ? $hotjar_code : '';
      echo $hotjar_code;

    ?>
    <!-- Hotjar code on custom field with Shortcodes --><?php
    
    //VWO Main Code Activation Flag
    $vwo_test_flag =  get_post_meta(get_the_ID(), 'vwo_test_check', true);  

    if(!$vwo_test_flag){
        addExternalScript('google-tag-manager-head-script'); 
    } ?>
  
</head>
<body <?= body_class(); ?>>
<p class="sr-only">Home Loan Experts</p><?php


//VWO Main Code Activation
if($vwo_test_flag){
    addExternalScript('VWO-control-page'); 
}else{
    addExternalScript('google-tag-manager-body-script');
} ?>


<!-- Main navigation in the header section -->
<header class="header"><?php
$header_brand_logo =  ImageData( get_option('options_wrs_header_brand_logo') ); 
$search_label = (get_option('options_wrs_header_search_label') != '') ? get_option('options_wrs_header_search_label') : 'Search for a topic...'; 

//Top Notification Banner
$wr_tp_notification_banner = get_post_meta(get_the_ID(),'wr_tp_notification_banner', true);
$wr_notification_banner_type = get_post_meta(get_the_ID(),'wr_notification_banner_type', true);
$wr_notification_banner_content = get_post_meta(get_the_ID(),'wr_notification_banner_content', true);

//Top Green 360 HLE Accessor Banner
$header_notification_banner_enable = get_option('options_header_notification_banner_enable');
$header_notification_banner_content = get_option('options_header_notification_banner_content');
$threesixty_page_list = $wpdb->get_results("SELECT option_value FROM $wpdb->dbname.wp_options where option_name like 'options_360_home_loan_sidebar_%_page_list' ");
$page_list = array();
if(isset($threesixty_page_list) && !is_null($threesixty_page_list) && is_array($threesixty_page_list)){
    foreach($threesixty_page_list as $rows){
        array_push($page_list, $rows->option_value);
    }
}

if($header_notification_banner_enable == true && $header_notification_banner_content != '' && in_array(get_the_permalink(),$page_list) ){ ?>
    <div class="top__banner-bar green__banner" id="topbar-section" style="z-index: 1060;">
        <div class="wrapper">
            <?= $header_notification_banner_content; ?>
        </div>
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.85 1.53125L6.13125 5.25L9.85 8.96875L8.9125 9.90625L5.19375 6.1875L1.475 9.90625L0.5375 8.96875L4.25625 5.25L0.5375 1.53125L1.475 0.59375L5.19375 4.3125L8.9125 0.59375L9.85 1.53125Z" fill="white"></path>
</svg>
    </div><?php
}else{
    if($wr_tp_notification_banner){ ?>
        <div class="top__banner-bar <?= $wr_notification_banner_type; ?>" id="topbar-section" style="z-index: 1060;">
            <div class="wrapper">
                <?= $wr_notification_banner_content; ?>

                       <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.85 1.53125L6.13125 5.25L9.85 8.96875L8.9125 9.90625L5.19375 6.1875L1.475 9.90625L0.5375 8.96875L4.25625 5.25L0.5375 1.53125L1.475 0.59375L5.19375 4.3125L8.9125 0.59375L9.85 1.53125Z" fill="white"/>
</svg>
            </div>
     

        </div>
<?php 
    }
} ?>
<nav class="navbar navbar-expand" role="navigation" aria-label="Main menu">
    <p class="sr-only">Main Navigation of Home Loan Experts</p>
    <?php
    if(isset($header_brand_logo) && !is_null($header_brand_logo) && is_array($header_brand_logo)){ 
        
        //Set Brand URL for Header Logo
        $brand_url = site_url(); 
        if(is_page_template('hle-website-redesign/tpl-hle-redesign-homepage.php')){
            $brand_url = "javascript:void(0)";
        } ?>
        <a href="<?= $brand_url; ?>" class="navbar-brand">
            <img class="navbar-brand--logo" src="<?= $header_brand_logo['src']; ?>" alt="Home Loan Experts" title="<?= $header_brand_logo['title']; ?>" height="34" width="140">
        </a><?php
    } ?>
    <div class="navbar-collapse"><?php
        wp_nav_menu(
            array(
                'theme_location' => 'wrp_header_menu',
                'menu_id' => 'wrp_header_menu',
                'container' => false,
                'menu_class'=>'navbar-nav mr-auto',
            )
        );  ?> 
        <form class="searchbox-menu" action="//www.homeloanexperts.com.au/search/" method="get" id="searchForm">
            <div class="search-wrap">
                <input type="text" class="form-control form-control--search" name="q"
                    placeholder="<?= $search_label; ?>" id="menuSearch">
                <div class="inner-addon left-addon">
                    <button type="button" class="btn btn-primary--blue btn-search" id="btnSearch">          
                        <svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_1718_104)">
                            <path d="M14.2748 12.25H13.5835L13.3385 12.0138C14.196 11.0163 14.7123 9.72125 14.7123 8.3125C14.7123 5.17125 12.166 2.625 9.02478 2.625C5.88353 2.625 3.33728 5.17125 3.33728 8.3125C3.33728 11.4538 5.88353 14 9.02478 14C10.4335 14 11.7285 13.4838 12.726 12.6263L12.9623 12.8713V13.5625L17.3373 17.9288L18.641 16.625L14.2748 12.25ZM9.02478 12.25C6.84603 12.25 5.08728 10.4913 5.08728 8.3125C5.08728 6.13375 6.84603 4.375 9.02478 4.375C11.2035 4.375 12.9623 6.13375 12.9623 8.3125C12.9623 10.4913 11.2035 12.25 9.02478 12.25Z" fill="white"/>
                            </g>
                            <defs>
                            <clipPath id="clip0_1718_104">
                            <rect width="21" height="21" fill="white" transform="translate(0.71228)"/>
                            </clipPath>
                            </defs>
                        </svg>

                    </button>
                    <input type="hidden" value="011453204810077047756:blhfrcksxio" name="cx">
                    <input type="hidden" value="FORID:10" name="cof">
                    <input name="siteurl" type="hidden" value="https://www.homeloanexperts.com.au/search/">
                </div>
            </div>
        </form>
    </div><?php
    if(!two_cta_header_test(get_the_ID())){ 
        //This is Regular Old CTA Header ?> 
        <div class="btn-group navbar-cta-grp"><?php
            //Get User's Country 
            $local_number = get_option('options_wrs_phone_number_australia');
            $aus_number = get_option('options_wrs_phone_number_other'); ?>            
            <a href="tel:<?= $aus_number; ?>" class="btn btn-lg btn-outline-primary--blue navbar-cta navbar-cta--ph">
                <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_1718_108)"><path d="M5.12561 7.19333C6.08561 9.08 7.63228 10.62 9.51895 11.5867L10.9856 10.12C11.1656 9.94 11.4323 9.88 11.6656 9.96C12.4123 10.2067 13.2189 10.34 14.0456 10.34C14.4123 10.34 14.7123 10.64 14.7123 11.0067V13.3333C14.7123 13.7 14.4123 14 14.0456 14C7.78561 14 2.71228 8.92667 2.71228 2.66667C2.71228 2.3 3.01228 2 3.37895 2H5.71228C6.07895 2 6.37895 2.3 6.37895 2.66667C6.37895 3.5 6.51228 4.3 6.75895 5.04667C6.83228 5.28 6.77895 5.54 6.59228 5.72667L5.12561 7.19333Z" class="icon-path"></path>
                    </g>
                    <defs>
                    <clipPath id="clip0_1718_108">
                        <rect width="16" height="16" fill="white" transform="translate(0.71228)"></rect>
                    </clipPath>
                    </defs>
                </svg>
                <span class="text"><?= $local_number; ?></span>
            </a><?php
            $header_link = get_option('options_wrs_header_link');
            $fqs_test_pages = [13269, 2435, 13404, 63666, 71307, 70491, 290, 55783, 84408]; 
            if(isset($header_link) && !is_null($header_link) && is_array($header_link)){

                $url = get_the_permalink();
        
                $three_sixty_flag = threesixty_accessor_check( $url );

                $header_link_title = (($header_link['title'] != '')) ? $header_link['title'] : 'Get a <strong>free</strong> assessment'; 
                
                //CASE 1: For 360 Specific Pages
                if($three_sixty_flag){

                    $header_link_url = site_url().'/360-home-loan-assessor/';

                }else{
                    
                    $header_link_url = ($header_link['url'] != '') ? $header_link['url'] : '/free-quote/'; 
                
                }

                //CASE 2: Free- Quote Static Pages
                if(in_array(get_the_ID(), $fqs_test_pages)){
                    $header_link_url = '/free-quote-static/';
                }

                //CASE 3: Free- Quote Doctors or Guarantors (Highest Precedence)
                if(free_quote_doctors_test(get_the_ID()) || free_quote_guarantors_test(get_the_ID())){
                    if(free_quote_doctors_test(get_the_ID())){
                        $header_link_url = '/free-quote-doctor/'; //For Doctors Niche Pages
                    }else{
                        $header_link_url = '/free-quote-guarantor-loan/'; //For Guarantor Niche Pages
                    }
                }

                //Case 4: Reverse Proxy URL for Homepage
                if ( is_front_page() ) {
                    $header_link_url = '/free-quote-v2/';
                }
            }
            $header_link_target = (isset($header_link['target'])) ? $header_link['target'] : '_self'; ?>
            <a href="<?= $header_link_url; ?>" class="btn btn-lg btn-primary--orange text-transform-uppercase btn-assessment navbar-cta navbar-cta--enquiry" id="enquiry_header_cta" target="<?= $header_link_target; ?>"><svg width="22" height="21" viewBox="0 0 22 21" fill="none" xmlns="http:www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1740_5531)">
                <path d="M17.9138 3.5H3.91379C2.95129 3.5 2.17254 4.2875 2.17254 5.25L2.16379 15.75C2.16379 16.7125 2.95129 17.5 3.91379 17.5H17.9138C18.8763 17.5 19.6638 16.7125 19.6638 15.75V5.25C19.6638 4.2875 18.8763 3.5 17.9138 3.5ZM17.9138 7L10.9138 11.375L3.91379 7V5.25L10.9138 9.625L17.9138 5.25V7Z" fill="white"/>
                </g>
                <defs>
                <clipPath id="clip0_1740_5531">
                <rect width="21" height="21" fill="white" transform="translate(0.413788)"/>
                </clipPath>
                </defs>
                </svg>
                <span class="text"><?= $header_link_title; ?></span>
            </a>
            <!-- hamgurer menu -->
            <button class="btn btn-hamburger hamburger" id="navHamburger" role="button" aria-label="Expand Menu">
                <span class="hamburger-line"></span>
            </button>
        </div><?php
    }else{ 
        //This is 2CTA in Header ?>
        <!-- New Button CTA header -->
        <div class="btn-group navbar-cta-grp navbar-cta--action">
            <a href="/360-home-loan-assessor/" id="two-cta-header-free-assessment" class="btn btn-md btn-outline-primary--blue navbar-cta navbar-cta--ph">
                <span class="text">GET A FREE ASSESSMENT</span> <svg class="navbar-cta__icon" width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><mask id="a" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24"><path fill="#D9D9D9" d="M0 0h24v24H0z"/></mask><g mask="url(#a)"><path d="M5.55 19 2 15.45l1.4-1.4 2.125 2.125 4.25-4.25 1.4 1.425L5.55 19zm0-8L2 7.45l1.4-1.4 2.125 2.125 4.25-4.25 1.4 1.425L5.55 11zM13 17v-2h9v2h-9zm0-8V7h9v2h-9z" fill="#27ADE7"/></g></svg>
            </a>
            
            <!-- Main navbar CTA dropdown component -->
            <div class=" navbar-cta--expert">
                <button class="btn btn-md btn-primary--orange navbar-cta  navbar-cta__trigger" id="two-cta-header-connect-to-an-expert">
                    <span class="text"> Connect with an Expert  </span> <svg class="navbar-cta__icon" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.0123 14.975C11.8789 14.975 11.7539 14.9542 11.6373 14.9125C11.5206 14.8709 11.4123 14.8 11.3123 14.7L6.71226 10.1C6.52892 9.91672 6.43726 9.68338 6.43726 9.40005C6.43726 9.11672 6.52892 8.88338 6.71226 8.70005C6.89559 8.51672 7.12892 8.42505 7.41226 8.42505C7.69559 8.42505 7.92892 8.51672 8.11226 8.70005L12.0123 12.6L15.9123 8.70005C16.0956 8.51672 16.3289 8.42505 16.6123 8.42505C16.8956 8.42505 17.1289 8.51672 17.3123 8.70005C17.4956 8.88338 17.5873 9.11672 17.5873 9.40005C17.5873 9.68338 17.4956 9.91672 17.3123 10.1L12.7123 14.7C12.6123 14.8 12.5039 14.8709 12.3873 14.9125C12.2706 14.9542 12.1456 14.975 12.0123 14.975Z" fill="white"/>
                    </svg>
                    <svg class="navbar-cta__icon--mobile" width="19" height="15" viewBox="0 0 19 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M16.4138 0.97998H2.41382C1.45132 0.97998 0.672568 1.76748 0.672568 2.72998L0.663818 13.23C0.663818 14.1925 1.45132 14.98 2.41382 14.98H16.4138C17.3763 14.98 18.1638 14.1925 18.1638 13.23V2.72998C18.1638 1.76748 17.3763 0.97998 16.4138 0.97998ZM16.4138 4.47998L9.41383 8.85498L2.41382 4.47998V2.72998L9.41383 7.10498L16.4138 2.72998V4.47998Z" fill="white"/>
                    </svg>

                </button>
            
                <div class="navbar-cta__dropdown">
                    <span class="navbar-cta__dropdown--title">Talk to a Broker</span>
                    <a href="/free-quote/" id="two-cta-header-enquire-now" class="btn btn-md btn-primary--orange navbar-cta__dropdown-item navbar-cta__dropdown-item--enquire">
                        <strong> ENQUIRE </strong> NOW
                    </a>
                    
                    <a href="tel:1300889743" id="two-cta-header-call-btn" class="btn btn-md btn-primary--blue navbar-cta__dropdown-item navbar-cta__dropdown-item--phone">
                        <svg class="navbar-cta__phone-icon" viewBox="0 0 24 24">
                            <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/>
                        </svg>
                        1300 889 743
                    </a>
                    <div class="navbar-cta__close-icon" aria-label="Close" role="button"></div>
                </div>
            </div>

            <!-- hamgurer menu -->
            <button class="btn btn-hamburger hamburger" id="navHamburger" role="button" aria-label="Expand Menu">
                <span class="hamburger-line"></span>
            </button>
        </div><?php
    } ?>
</nav>
<div class="progress-container">
    <div class="progress-bar" id="myBar"></div>
</div>
</header>
<!-- Closing of header-->
<div class="header-spacer"></div>
