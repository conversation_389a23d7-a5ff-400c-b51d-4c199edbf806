<?php
   wp_footer();
   $footer_brand_logo = ImageData(get_option("options_wrs_footer_brand_logo"));
   $about_us_block = get_option("options_wrs_footer_about_us_block");
   $copyright_block = get_option("options_wrs_footer_copyright_text");
   $footer_local_telephone = get_option("options_wrs_footer_local_telephone");
   $footer_fax_number = get_option("options_wrs_footer_fax_number");
   $footer_mail_address = get_option("options_wrs_footer_mail_address");
   $footer_overseas_telephone = get_option(
       "options_wrs_footer_overseas_telephone"
   );
   $footer_copyright_text = get_option("options_wrs_footer_copyright_text");
   
   //For Footer Top Information Block
   $footer_info_block = $wpdb->get_results(
       "SELECT option_name, option_value FROM $wpdb->dbname.wp_options where option_name like 'options_wrs_footer_company_information_block_%' "
   );
   $footer_info_block_data = [];
   $count_footer_info_block = count($footer_info_block);
   $i = 0;
   foreach ($footer_info_block as $row) {
       if (str_contains($row->option_name, "information_block")) {
           $footer_info_block_data[$i]["information_block"] = $row->option_value;
           $i++;
       }
   }
   
   //For Social Media Block
   $social_media = $wpdb->get_results(
       "SELECT option_name, option_value FROM $wpdb->dbname.wp_options where option_name like 'options_wrs_social_media_link_%' "
   );
   $social_media_data = [];
   $count_social_media = count($social_media);
   $i = 0;
   $j = 0;
   foreach ($social_media as $row) {
       if (str_contains($row->option_name, "_icon")) {
           $social_media_data[$i]["icon"] = $row->option_value;
       }
   
       if (str_contains($row->option_name, "_title")) {
           $social_media_data[$i]["title"] = $row->option_value;
       }
   
       if (str_contains($row->option_name, "_url")) {
           $social_media_data[$i]["url"] = $row->option_value;
       }
   
       $j++;
       if ($j % 3 == 0) {
           $i++;
       }
   }
   ?> 
<footer class="footer">
   <div class="footer-top">
      <div class="container">
         <div class="row"><?php 
          if ((isset($footer_brand_logo) && !is_null($footer_brand_logo) && is_array($footer_brand_logo)) || $about_us_block != ""): ?>
              <div class="col-md-6 col-lg-3"><?php
                if (isset($footer_brand_logo) && !is_null($footer_brand_logo) && is_array($footer_brand_logo)) { ?>
                  <div class="footer-logo">
                    <img src="<?= $footer_brand_logo["src"] ?>" alt="<?= $footer_brand_logo[
                       "alt"
                       ] ?>" loading="lazy" height="41" width="165">
                  </div><?php 
                }
                if ($about_us_block != "") { ?>
                  <div class="footer-menu">
                    <h5 class="footer-menu--title mb-0">About US</h5>
                    <p class="footer-menu--content"><?= $about_us_block ?></p>
                  </div><?php 
                }?>
            </div><?php 
          endif; ?>
          <div class="col-md-6 col-lg-3">
            <div class="footer-menu">
              <h5 class="footer-menu--title">
                 Popular Links 
                 <svg class="icon" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#fff">
                    <path d="M0 0h24v24H0z" fill="none"/>
                    <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/>
                 </svg>
              </h5>
              <?php wp_nav_menu(
                 [
                     "theme_location" => "wrp_footer_1",
                     "menu_id" => "wrp_footer_1",
                     "container" => false,
                     "menu_class" => "footer-menu-list",
                 ]
                 ); ?> 
            </div>
            </div>
            <div class="col-md-6 col-lg-3">
               <div class="footer-menu">
                  <h5 class="footer-menu--title">
                     Specialities 
                     <svg class="icon" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#fff">
                        <path d="M0 0h24v24H0z" fill="none"/>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/>
                     </svg>
                  </h5>
                  <?php wp_nav_menu(
                     [
                         "theme_location" => "wrp_footer_2",
                         "menu_id" => "wrp_footer_2",
                         "container" => false,
                         "menu_class" => "footer-menu-list",
                     ]
                     ); ?>
               </div>
            </div>
            <div class="col-md-6 col-lg-3">
               <div class="footer-menu">
                  <h5 class="footer-menu--title">
                     Tools & Resources 
                     <svg class="icon" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#fff">
                        <path d="M0 0h24v24H0z" fill="none"/>
                        <path d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"/>
                     </svg>
                  </h5>
                  <?php wp_nav_menu(
                     [
                         "theme_location" => "wrp_footer_3",
                         "menu_id" => "wrp_footer_3",
                         "container" => false,
                         "menu_class" => "footer-menu-list",
                     ]
                     ); ?>
               </div>
            </div>
         </div>
         <div class="footer-address">
            <?php
               if (
                   isset($footer_info_block_data) &&
                   !is_null($footer_info_block_data)
               ) { ?>
            <div class="footer-address--top">
               <?php foreach (
                  $footer_info_block_data
                  as $rows
                  ) { ?>
               <div class="footer-address-item">
                  <?= $rows["information_block"] ?>
               </div>
               <?php } ?>
            </div>
            <?php }
               if (
                   $footer_local_telephone != "" ||
                   $footer_fax_number != "" ||
                   $footer_overseas_telephone != "" ||
                   $footer_mail_address != ""
               ) { ?>
            <div class="footer-address--bottom"><?php
               if ($footer_local_telephone != "") {
                   echo '<a href="tel:' .
                       $footer_local_telephone .
                       '" class="content-contact" title="Local Phone Number"> <svg class="icon" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#1380af"><path d="M0 0h24v24H0z" fill="none"/><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"/></svg><strong>Local:</strong>&nbsp;' .
                       $footer_local_telephone .
                       "</a>";
               }
               
               if ($footer_overseas_telephone != "") {
                   echo '<a class="content-contact" href="tel:' .
                       $footer_overseas_telephone .
                       '" title="Overseas Phone Number"><svg class="icon" xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#1380af"><path d="M0 0h24v24H0z" fill="none"/><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm6.93 6h-2.95c-.32-1.25-.78-2.45-1.38-3.56 1.84.63 3.37 1.91 4.33 3.56zM12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96zM4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2 0 .68.06 1.34.14 2H4.26zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56-1.84-.63-3.37-1.9-4.33-3.56zm2.95-8H5.08c.96-1.66 2.49-2.93 4.33-3.56C8.81 5.55 8.35 6.75 8.03 8zM12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96zM14.34 14H9.66c-.09-.66-.16-1.32-.16-2 0-.68.07-1.35.16-2h4.68c.09.65.16 1.32.16 2 0 .68-.07 1.34-.16 2zm.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95c-.96 1.65-2.49 2.93-4.33 3.56zM16.36 14c.08-.66.14-1.32.14-2 0-.68-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2h-3.38z"/></svg><strong>Overseas: </strong>&nbsp;' .
                       $footer_overseas_telephone .
                       "</a>";
               }
               
               if ($footer_fax_number != "") {
                   echo '<p class="content-contact fax"> <svg class="icon" xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                       <g clip-path="url(#clip0_3719_857)">
                       <path d="M16.3333 6.66667H4.66666C3.28333 6.66667 2.16666 7.78333 2.16666 9.16667V14.1667H5.5V17.5H15.5V14.1667H18.8333V9.16667C18.8333 7.78333 17.7167 6.66667 16.3333 6.66667ZM13.8333 15.8333H7.16666V11.6667H13.8333V15.8333ZM16.3333 10C15.875 10 15.5 9.625 15.5 9.16667C15.5 8.70833 15.875 8.33333 16.3333 8.33333C16.7917 8.33333 17.1667 8.70833 17.1667 9.16667C17.1667 9.625 16.7917 10 16.3333 10ZM15.5 2.5H5.5V5.83333H15.5V2.5Z" fill="#1380AF"/>
                       </g>
                       <defs>
                       <clipPath id="clip0_3719_857">
                       <rect width="20" height="20" fill="white" transform="translate(0.5)"/>
                       </clipPath>
                       </defs>
                       </svg><strong>Fax: </strong>&nbsp;' .
                       $footer_fax_number .
                       "</p>";
               }
               
               if ($footer_mail_address != "") {
                   echo '<a class="content-contact" href="mailto:' .
                       $footer_mail_address .
                       '" title ="Email for Information"><svg class="icon" xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                       <g clip-path="url(#clip0_3719_875)">
                       <path d="M17.1667 3.3335H3.83332C2.91666 3.3335 2.17499 4.0835 2.17499 5.00016L2.16666 15.0002C2.16666 15.9168 2.91666 16.6668 3.83332 16.6668H17.1667C18.0833 16.6668 18.8333 15.9168 18.8333 15.0002V5.00016C18.8333 4.0835 18.0833 3.3335 17.1667 3.3335ZM17.1667 6.66683L10.5 10.8335L3.83332 6.66683V5.00016L10.5 9.16683L17.1667 5.00016V6.66683Z" fill="#1380AF"/>
                       </g>
                       <defs>
                       <clipPath id="clip0_3719_875">
                       <rect width="20" height="20" fill="white" transform="translate(0.5)"/>
                       </clipPath>
                       </defs>
                       </svg>' .
                       $footer_mail_address .
                       "</a>";
               }
               ?>
            </div>
            <?php }
               //endif
               ?>
         </div>
      </div>
   </div>
   <?php if (
      $footer_copyright_text != "" ||
      (isset($social_media) &&
          !is_null($social_media) &&
          $social_media != "")
      ) { ?>
   <div class="footer-bottom">
      <div class="container">
         <div class="row">
            <?php
               if ($footer_copyright_text != "") { ?>
            <div class="col-md-6">
               <p class="footer-copy"><?= $footer_copyright_text ?></p>
            </div>
            <?php }
               if (
                   isset($social_media_data) &&
                   !is_null($social_media_data) &&
                   $social_media_data != ""
               ) { ?>
            <div class="col-md-6">
               <ul class="list-social-media">
                  <?php foreach (
                     $social_media_data
                     as $rows
                     ) {
                     if ($rows["icon"] != "") { ?>
                  <li class="list-item">
                     <a href="<?= $rows[
                        "url"
                        ] ?>" class="list-link" title="<?= $rows[
                        "title"
                        ] ?>" target="_blank">
                        <!-- Social Media SVG Icon -->
                        <?= $rows["icon"] ?>
                     </a>
                  </li>
                  <?php }
                     } ?>               
               </ul>
            </div>
            <?php }
               ?>
         </div>
      </div>
   </div>
   <?php }
      //endif
      ?>
</footer>
<a id="scrollTopButton" aria-label="Scroll to top" href="javascript:void(0);" class="scroll--top"></a>
<?php if (is_user_logged_in() && current_user_can("edit_posts")) {
   edit_post_link(
       __("Edit", "hle"),
       '<span class="edit-link">',
       "</span>"
   );
   } ?>
<!-- Floating Contact (Default: Collapsed) --><?php
if(floating_footer_test(get_the_ID()) && wp_is_mobile()){ ?>
  <div class="floating-contact" id="floatingContact" style="display: none;">
     <!-- Toggle Button (Blue Question Mark) -->
     <button class="floating-contact__toggle" id="contactToggle" aria-label="Open contact options">
        <span class="floating-contact__icon">
           <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M11.75 19H11.5C9.13333 19 7.125 18.175 5.475 16.525C3.825 14.875 3 12.8667 3 10.5C3 8.13333 3.825 6.125 5.475 4.475C7.125 2.825 9.13333 2 11.5 2C12.6833 2 13.7875 2.22083 14.8125 2.6625C15.8375 3.10417 16.7375 3.7125 17.5125 4.4875C18.2875 5.2625 18.8958 6.1625 19.3375 7.1875C19.7792 8.2125 20 9.31667 20 10.5C20 12.7333 19.3708 14.8083 18.1125 16.725C16.8542 18.6417 15.2667 20.1417 13.35 21.225C13.1833 21.3083 13.0167 21.3542 12.85 21.3625C12.6833 21.3708 12.5333 21.3333 12.4 21.25C12.2667 21.1667 12.15 21.0583 12.05 20.925C11.95 20.7917 11.8917 20.6333 11.875 20.45L11.75 19ZM11.475 15.975C11.7583 15.975 12 15.875 12.2 15.675C12.4 15.475 12.5 15.2333 12.5 14.95C12.5 14.6667 12.4 14.425 12.2 14.225C12 14.025 11.7583 13.925 11.475 13.925C11.1917 13.925 10.95 14.025 10.75 14.225C10.55 14.425 10.45 14.6667 10.45 14.95C10.45 15.2333 10.55 15.475 10.75 15.675C10.95 15.875 11.1917 15.975 11.475 15.975ZM9.3 8.375C9.48333 8.45833 9.66667 8.4625 9.85 8.3875C10.0333 8.3125 10.1833 8.19167 10.3 8.025C10.45 7.825 10.625 7.67083 10.825 7.5625C11.025 7.45417 11.25 7.4 11.5 7.4C11.9 7.4 12.225 7.5125 12.475 7.7375C12.725 7.9625 12.85 8.25 12.85 8.6C12.85 8.81667 12.7875 9.03333 12.662l5 9.25C12.5375 9.46667 12.3167 9.73333 12 10.05C11.5833 10.4167 11.275 10.7625 11.075 11.0875C10.875 11.4125 10.775 11.7417 10.775 12.075C10.775 12.275 10.8458 12.4458 10.9875 12.5875C11.1292 12.7292 11.3 12.8 11.5 12.8C11.7 12.8 11.8667 12.725 12 12.575C12.1333 12.425 12.2333 12.25 12.3 12.05C12.3833 11.7667 12.5333 11.5083 12.75 11.275C12.9667 11.0417 13.1667 10.8333 13.35 10.65C13.7 10.3 13.9625 9.95 14.1375 9.6C14.3125 9.25 14.4 8.9 14.4 8.55C14.4 7.78333 14.1375 7.16667 13.6125 6.7C13.0875 6.23333 12.3833 6 11.5 6C10.9667 6 10.475 6.12917 10.025 6.3875C9.575 6.64583 9.20833 7 8.925 7.45C8.825 7.63333 8.8125 7.8125 8.8875 7.9875C8.9625 8.1625 9.1 8.29167 9.3 8.375Z" fill="white"/>
           </svg>
        </span>
     </button>
     <!-- Expanded Contact Options -->
     <div class="floating-contact__options" id="contactOptions">
         <!-- Zopim Chatbot Button Start-->
           <a id="zopim-floating-chat-initialize" href="javascript:void(0)" class="floating-contact__option floating-contact__option--chat" aria-label="Chat with us" style="display: none;">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                 <path d="M3.99998 12.5L2.46665 14.0333C2.25554 14.2444 2.01387 14.2916 1.74165 14.175C1.46942 14.0583 1.33331 13.85 1.33331 13.55V3.16665C1.33331 2.79998 1.46387 2.48609 1.72498 2.22498C1.98609 1.96387 2.29998 1.83331 2.66665 1.83331H13.3333C13.7 1.83331 14.0139 1.96387 14.275 2.22498C14.5361 2.48609 14.6666 2.79998 14.6666 3.16665V11.1666C14.6666 11.5333 14.5361 11.8472 14.275 12.1083C14.0139 12.3694 13.7 12.5 13.3333 12.5H3.99998Z" fill="#27ADE7"/>
              </svg>
              <span class="floating-contact__label"><strong>Chat </strong> with us</span>
           </a>
         <!-- Zopim Chatbot Button Ends-->
         <!-- Morty Chatbot Button Starts -->
           <a id="morty-floating-chat-initialize" href="javascript:void(0)" class="floating-contact__option floating-contact__option--chat" aria-label="Chat with us" style="display: none;">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                 <path d="M3.99998 12.5L2.46665 14.0333C2.25554 14.2444 2.01387 14.2916 1.74165 14.175C1.46942 14.0583 1.33331 13.85 1.33331 13.55V3.16665C1.33331 2.79998 1.46387 2.48609 1.72498 2.22498C1.98609 1.96387 2.29998 1.83331 2.66665 1.83331H13.3333C13.7 1.83331 14.0139 1.96387 14.275 2.22498C14.5361 2.48609 14.6666 2.79998 14.6666 3.16665V11.1666C14.6666 11.5333 14.5361 11.8472 14.275 12.1083C14.0139 12.3694 13.7 12.5 13.3333 12.5H3.99998Z" fill="#27ADE7"/>
              </svg>
              <span class="floating-contact__label"><strong>Chat </strong> with us</span>
           </a>
         <!-- Morty Chatbot Button Ends -->
        <a href="tel:1300 889 743" id="floating-chat-contact" class="floating-contact__option floating-contact__option--call" aria-label="Call us now">
           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
              <path d="M13.3 14.5C11.9111 14.5 10.5389 14.1972 9.18333 13.5917C7.82778 12.9861 6.59444 12.1278 5.48333 11.0167C4.37222 9.90556 3.51389 8.67222 2.90833 7.31667C2.30278 5.96111 2 4.58889 2 3.2C2 3 2.06667 2.83333 2.2 2.7C2.33333 2.56667 2.5 2.5 2.7 2.5H5.4C5.55556 2.5 5.69444 2.55278 5.81667 2.65833C5.93889 2.76389 6.01111 2.88889 6.03333 3.03333L6.46667 5.36667C6.48889 5.54444 6.48333 5.69444 6.45 5.81667C6.41667 5.93889 6.35556 6.04444 6.26667 6.13333L4.65 7.76667C4.87222 8.17778 5.13611 8.575 5.44167 8.95833C5.74722 9.34167 6.08333 9.71111 6.45 10.0667C6.79444 10.4111 7.15556 10.7306 7.53333 11.025C7.91111 11.3194 8.31111 11.5889 8.73333 11.8333L10.3 10.2667C10.4 10.1667 10.5306 10.0917 10.6917 10.0417C10.8528 9.99167 11.0111 9.97778 11.1667 10L13.4667 10.4667C13.6222 10.5111 13.75 10.5917 13.85 10.7083C13.95 10.825 14 10.9556 14 11.1V13.8C14 14 13.9333 14.1667 13.8 14.3C13.6667 14.4333 13.5 14.5 13.3 14.5Z" fill="#FB7925"/>
           </svg>
           <span class="floating-contact__label"><strong>Call us </strong> now</span>
        </a>
        <a href="/free-quote/" id="floating-chat-enquiry" class="floating-contact__option floating-contact__option--callback" aria-label="Request callback">
           <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
              <path d="M13.3 14.5C11.9111 14.5 10.5389 14.1972 9.18333 13.5917C7.82778 12.9861 6.59444 12.1278 5.48333 11.0167C4.37222 9.90556 3.51389 8.67222 2.90833 7.31667C2.30278 5.96111 2 4.58889 2 3.2C2 3 2.06667 2.83333 2.2 2.7C2.33333 2.56667 2.5 2.5 2.7 2.5H5.4C5.55556 2.5 5.69444 2.55278 5.81667 2.65833C5.93889 2.76389 6.01111 2.88889 6.03333 3.03333L6.46667 5.36667C6.48889 5.54444 6.48333 5.69444 6.45 5.81667C6.41667 5.93889 6.35556 6.04444 6.26667 6.13333L4.65 7.76667C4.87222 8.17778 5.13611 8.575 5.44167 8.95833C5.74722 9.34167 6.08333 9.71111 6.45 10.0667C6.79444 10.4111 7.15556 10.7306 7.53333 11.025C7.91111 11.3194 8.31111 11.5889 8.73333 11.8333L10.3 10.2667C10.4 10.1667 10.5306 10.0917 10.6917 10.0417C10.8528 9.99167 11.0111 9.97778 11.1667 10L13.4667 10.4667C13.6222 10.5111 13.75 10.5917 13.85 10.7083C13.95 10.825 14 10.9556 14 11.1V13.8C14 14 13.9333 14.1667 13.8 14.3C13.6667 14.4333 13.5 14.5 13.3 14.5ZM10.9333 6.5H12C12.1889 6.5 12.3472 6.56389 12.475 6.69167C12.6028 6.81944 12.6667 6.97778 12.6667 7.16667C12.6667 7.35556 12.6028 7.51389 12.475 7.64167C12.3472 7.76944 12.1889 7.83333 12 7.83333H9.33333C9.14444 7.83333 8.98611 7.76944 8.85833 7.64167C8.73056 7.51389 8.66667 7.35556 8.66667 7.16667V4.5C8.66667 4.31111 8.73056 4.15278 8.85833 4.025C8.98611 3.89722 9.14444 3.83333 9.33333 3.83333C9.52222 3.83333 9.68056 3.89722 9.80833 4.025C9.93611 4.15278 10 4.31111 10 4.5V5.56667L12.8667 2.7C12.9889 2.57778 13.1444 2.51667 13.3333 2.51667C13.5222 2.51667 13.6778 2.57778 13.8 2.7C13.9222 2.82222 13.9833 2.97778 13.9833 3.16667C13.9833 3.35556 13.9222 3.51111 13.8 3.63333L10.9333 6.5Z" fill="white"/>
           </svg>
           <span class="floating-contact__label">Request <strong>Callback </strong></span>
        </a>
        <button class="floating-contact__close" id="contactClose" aria-label="Close contact options">
           <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 13.4L7.09999 18.3C6.91665 18.4833 6.68332 18.575 6.39999 18.575C6.11665 18.575 5.88332 18.4833 5.69999 18.3C5.51665 18.1167 5.42499 17.8833 5.42499 17.6C5.42499 17.3167 5.51665 17.0833 5.69999 16.9L10.6 12L5.69999 7.09999C5.51665 6.91665 5.42499 6.68332 5.42499 6.39999C5.42499 6.11665 5.51665 5.88332 5.69999 5.69999C5.88332 5.51665 6.11665 5.42499 6.39999 5.42499C6.68332 5.42499 6.91665 5.51665 7.09999 5.69999L12 10.6L16.9 5.69999C17.0833 5.51665 17.3167 5.42499 17.6 5.42499C17.8833 5.42499 18.1167 5.51665 18.3 5.69999C18.4833 5.88332 18.575 6.11665 18.575 6.39999C18.575 6.68332 18.4833 6.91665 18.3 7.09999L13.4 12L18.3 16.9C18.4833 17.0833 18.575 17.3167 18.575 17.6C18.575 17.8833 18.4833 18.1167 18.3 18.3C18.1167 18.4833 17.8833 18.575 17.6 18.575C17.3167 18.575 17.0833 18.4833 16.9 18.3L12 13.4Z" fill="#1380AF"/>
           </svg>
        </button>
     </div>
  </div><?php
} ?>
</body>
</html>