document.addEventListener("DOMContentLoaded", function () {
  //Start of Zopim Live Chat Script

  (function() {
    var hostUrl = getHost();
    var ignoredPathList = ["overall-assessment", "free-quote-static", "free-quote-new", "lpnew", "360-home-loan-assessor", "free-quote", "enquiry-thank-you"];
    var currentURL = window.location.href;
    var parsedURL = new URL(currentURL);
    var path = parsedURL.pathname;
    var brokerOnline = false;
    var executed = false;
    var observer;
    var initialized = false;
    var offOfficeHour = checkActiveHour();

    function checkActiveHour() {
      var sydneyTimeZone = 'Australia/Sydney';
      var currentDateInSydney = new Date().toLocaleString('en-US', { timeZone: sydneyTimeZone });

      // Extract current hour, day, and minutes
      var currentHour = new Date(currentDateInSydney).getHours();
      var currentDay = new Date(currentDateInSydney).getDay();
    
      // Check if the current time is between 8 PM and 9 AM or it's Saturday or Sunday
      if ((currentDay === 0 || currentDay === 6) || (currentHour >= 20 || currentHour < 9)) {
        return true;
      }

      // Return false for other cases
      return false;
    }

    //For Floating Chatbot Test
    function toggleChatButtons() {
      const morty = document.getElementById('morty-floating-chat-initialize');
      const zopim = document.getElementById('zopim-floating-chat-initialize');

      if (morty && zopim) {
          morty.style.display = "flex";
          zopim.style.display = "none";
      }
    }
    
    if (document.getElementById('morty-floating-chat-initialize') && offOfficeHour) {
        
        toggleChatButtons();
        initialize();
        document.getElementById("morty-floating-chat-initialize").addEventListener('click', function () {
          document.querySelector('.floating-chat-button').click();
        });
    
    }else{
    
      document.addEventListener('scroll', initialize);
      document.addEventListener('mousedown', initialize);
      document.addEventListener('mousemove', initialize);
      document.addEventListener('touchstart', initialize);
      document.addEventListener('keydown', initialize);
    
    }

    // Function to remove element
    function removeElement(element) {
      try {
        var iframe1 = document.getElementById('launcher');
        var iframe2 = document.getElementById('webWidget');

        if (iframe1 || iframe2) {

          if (iframe1) iframe1.style.visibility = 'hidden';
          if (iframe2) iframe2.style.visibility = 'hidden';

        } else {
          console.warn('Element is not a child of its parent or does not exist.');
        }
      } catch (error) {
        console.error('Error removing element:', error);
      }
    }

    function checkStyleChange(element) {
      try {
        if (element) {
          var computedStyle = window.getComputedStyle(element);

          if (computedStyle.visibility !== "hidden") {
            removeElement(element);
          }
        }
      } catch (error) {
        console.error('Error checking style change:', error);
      }
    }

    function checkBrokerStatus() {
      try {
        var liveChatIds = ['launcher'];
        
        var iframe1 = document.getElementById('launcher');
        var iframe2 = document.getElementById('webWidget');
        
        if(iframe1 && iframe1.style){
          if(iframe1.style.opacity ==="1"){
            brokerOnline = true;
          }
          else if(iframe1.style.opacity === "0" && iframe1.style.visibility ==="hidden" && iframe2){
            brokerOnline = true;
          }
        }
        
        loadChatBotScript();
        
      } catch (error) {
        console.error('Error checking broker status:', error);
      }
    }

    function updateChatBoxStyles() {
      try {
        // Adjust chat-box-holder position based on sticky-enquiry form
        var chatBoxHolder = document.getElementById('chat-box-holder');
        //var stickyEnquiryForm = document.querySelector('._sticky-enquiry__form_13zdg_44');
        var stickyEnquiryForm = document.querySelector('.sticky-enquiry-wrapper');
        var stickyEnquiryDiv = document.querySelector('.sticky-enquiry-form');
        var floatingChatButton = document.querySelector('.floating-chat-button');
        var hiddenEnquiryClass = document.querySelector('.sticky-enquiry-form--hidden')
        
        if (chatBoxHolder) {
          if(!hiddenEnquiryClass && stickyEnquiryForm  && window.innerWidth <= 768){
            chatBoxHolder.style.visibility = 'hidden';
          } else {
            chatBoxHolder.style.visibility = 'visible';
          }
          
          if (stickyEnquiryForm) {
            // Get the height of the sticky enquiry form
            var stickyFormHeight = stickyEnquiryForm.offsetHeight;
            
            // Set the bottom position of chat-box-holder to the height of the sticky form
            chatBoxHolder.style.bottom = stickyFormHeight + 'px';
            if(window.innerWidth <= 768 && !floatingChatButton){
              chatBoxHolder.style.height = '95%';
            } else if(!floatingChatButton) {
              chatBoxHolder.style.height = 'auto';
            }
          } else {
            // If sticky form is not in the DOM, set bottom to a value
            chatBoxHolder.style.bottom = '0px';
          }
          
          // Set z-index of chat-box-holder based on presence of sticky enquiry div
          if (stickyEnquiryDiv) {
            chatBoxHolder.style.zIndex = '1001';
          } else {
            chatBoxHolder.style.zIndex = '9999999';
          }
          
          // Find the chat-box element (child of chat-box-holder)
          var chatBox = chatBoxHolder.querySelector('.chat-box');
          if (chatBox) {
            // Set custom styles
            chatBox.style.border = 'none';
            chatBox.style.boxShadow = '0px -8px 14px rgba(0, 0, 0, .04)';
          }
          
       // Try-catch specifically for floating-chat-button to handle cases where it might not exist
        try {
          var floatingChatButton = document.querySelector('.floating-chat-button');
          // Only proceed if floatingChatButton is found and is a valid DOM element
          if (floatingChatButton && floatingChatButton.style) {
            if (stickyEnquiryDiv) {
              floatingChatButton.style.zIndex = '999';
            } else {
              floatingChatButton.style.zIndex = '9999999';
            }
          }
        } catch (buttonError) {
          console.error('Error updating floating chat button:', buttonError);
        }
        }
      } catch (error) {
        console.error('Error updating chat box styles:', error);
      }
    }

    function loadChatBotScript() {
      try {
        document.removeEventListener('scroll', initialize);
        document.removeEventListener('mousedown', initialize);
        document.removeEventListener('mousemove', initialize);
        document.removeEventListener('touchstart', initialize);
        document.removeEventListener('keydown', initialize);

        if (ignoredPathList.some(function(keyword) { return path.includes(keyword); })) return;

        // Check if it's a dev site (anything other than production or UAT)
        const currentUrl = window.location.origin;
        const isDevSite = currentUrl !== "https://www.homeloanexperts.com.au" && currentUrl !== "https://hle.uat.homeloanexperts.net.au";

        // Single condition: execute if it's dev site OR if it's production/UAT and office hours condition is met
        if ((isDevSite || offOfficeHour) && !executed) {
          executed = true;
          fetch(hostUrl + 'asset-manifest.json')
            .then(function(res) {
              if (!res.ok) throw new Error('Network response was not ok');
              return res.json();
            })
            .then(function(data) {
              var chatBoxHolderElement = document.getElementById('chat-box-holder');
              if (!chatBoxHolderElement) {
                var holderElement = document.createElement('div');
                holderElement.id = 'chat-box-holder';
                document.body.appendChild(holderElement);
              }

              data.entrypoints.forEach(function(entry) {
                if (entry.endsWith('.js')) {
                  var script = document.createElement('script');
                  script.src = hostUrl + entry;
                  script.defer = true;
                  document.head.appendChild(script);
                } else {
                  var link = document.createElement('link');
                  link.href = hostUrl + entry;
                  link.rel = 'stylesheet';
                  document.head.insertBefore(link, document.head.firstChild);
                }
              });
              
              // Set up a mutation observer to watch for when the chat box is fully loaded
              // and to detect when sticky form is added or removed
              var chatBoxObserver = new MutationObserver(function(mutations) {
                updateChatBoxStyles();
              });
              
              chatBoxObserver.observe(document.body, { 
                childList: true, 
                subtree: true 
              });
              
              // Also set up a resize observer on the sticky form
              if (window.ResizeObserver) {
                var stickyFormResizeObserver = new ResizeObserver(function() {
                  updateChatBoxStyles();
                });
                
                var checkForStickyForm = setInterval(function() {
                  var stickyForm = document.querySelector('.sticky-enquiry-wrapper');
                  if (stickyForm) {
                    stickyFormResizeObserver.observe(stickyForm);
                    clearInterval(checkForStickyForm);
                  }
                }, 500);
              }
              
              // Initial update attempt
              updateChatBoxStyles();
            })
            .catch(function(error) {
              console.error('Error fetching chatbot script:', error);
            });

          var targetElement = document.getElementById('launcher');
          if (targetElement) {
            targetElement.setAttribute('data-lastVisibility', window.getComputedStyle(targetElement).visibility);

            observer = new MutationObserver(function(mutationsList) {
              mutationsList.forEach(function(mutation) {
                checkStyleChange(targetElement);
              });
            });

            observer.observe(targetElement, { attributes: true, attributeFilter: ['style'] });

            // Try to remove the element initially
            removeElement(targetElement);
          }
          }
        }
      } catch (error) {
        console.error('Error loading chatbot script:', error);
      }
    }

    function initialize() {
      try {
        if(!initialized){
          initialized = true;
          var observerOne = new MutationObserver(function(mutations, obs) {
            if (document.getElementById('launcher') || document.getElementById('morty-floating-chat-initialize')) {
              setTimeout(function() {
                checkBrokerStatus();
                obs.disconnect();
              }, 6000);
            }
          });
          observerOne.observe(document.body, { childList: true, subtree: true });
        }
      } catch (error) {
        console.error('Error initializing script:', error);
      }
    }

    function getHost(){
      const currentUrl = window.location.origin;

      if (currentUrl === "https://www.homeloanexperts.com.au") {
          return "https://hle-web-bot.homeloanexperts.com.au/";
      } else if (currentUrl === "https://hle.uat.homeloanexperts.net.au") {
         return "https://hle-web-bot.uat.homeloanexperts.net.au/";
      } else {
          return"https://hle-web-bot.dev.homeloanexperts.net.au/";
      }

    }
    
    // Set up an interval to periodically check for and update chat box styles
    // This helps ensure the styles are applied even if elements load in a different order
    var styleUpdateInterval = setInterval(function() {
      updateChatBoxStyles();
    }, 1000);
    
    // Clear the interval after 30 seconds to prevent unnecessary processing
    setTimeout(function() {
      clearInterval(styleUpdateInterval);
    }, 30000);
  })();
});