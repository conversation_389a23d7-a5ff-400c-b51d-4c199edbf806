{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "900f2d150850c83183e246d3752982c1", "packages": [{"name": "composer/installers", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "c29dc4b93137acb82734f672c37e029dfbd95b35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/c29dc4b93137acb82734f672c37e029dfbd95b35", "reference": "c29dc4b93137acb82734f672c37e029dfbd95b35", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^5.3", "symfony/process": "^5"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.2.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-08-20T06:45:11+00:00"}, {"name": "johnpbloch/wordpress", "version": "6.8.2", "source": {"type": "git", "url": "https://github.com/johnpbloch/wordpress.git", "reference": "236680349aff347c8bac55665245d3b347d162f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnpbloch/wordpress/zipball/236680349aff347c8bac55665245d3b347d162f0", "reference": "236680349aff347c8bac55665245d3b347d162f0", "shasum": ""}, "require": {"johnpbloch/wordpress-core": "6.8.2", "johnpbloch/wordpress-core-installer": "^1.0 || ^2.0", "php": ">=5.6.20"}, "type": "package", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "WordPress Community", "homepage": "https://wordpress.org/about/"}], "description": "WordPress is open source software you can use to create a beautiful website, blog, or app.", "homepage": "https://wordpress.org/", "keywords": ["blog", "cms", "wordpress"], "support": {"docs": "https://developer.wordpress.org/", "forum": "https://wordpress.org/support/", "irc": "irc://irc.freenode.net/wordpress", "issues": "https://core.trac.wordpress.org/", "source": "https://core.trac.wordpress.org/browser"}, "time": "2023-05-20T04:40:46+00:00"}, {"name": "johnpbloch/wordpress-core", "version": "6.8.2", "source": {"type": "git", "url": "https://github.com/johnpbloch/wordpress-core.git", "reference": "5ed6d4a0469ff42d7ec4a1f72558be8f897a6e18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnpbloch/wordpress-core/zipball/5ed6d4a0469ff42d7ec4a1f72558be8f897a6e18", "reference": "5ed6d4a0469ff42d7ec4a1f72558be8f897a6e18", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6.20"}, "provide": {"wordpress/core-implementation": "6.8.2"}, "type": "wordpress-core", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "WordPress Community", "homepage": "https://wordpress.org/about/"}], "description": "WordPress is open source software you can use to create a beautiful website, blog, or app.", "homepage": "https://wordpress.org/", "keywords": ["blog", "cms", "wordpress"], "support": {"forum": "https://wordpress.org/support/", "irc": "irc://irc.freenode.net/wordpress", "issues": "https://core.trac.wordpress.org/", "source": "https://core.trac.wordpress.org/browser", "wiki": "https://codex.wordpress.org/"}, "time": "2023-05-20T04:40:42+00:00"}, {"name": "johnpbloch/wordpress-core-installer", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/johnpbloch/wordpress-core-installer.git", "reference": "237faae9a60a4a2e1d45dce1a5836ffa616de63e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/johnpbloch/wordpress-core-installer/zipball/237faae9a60a4a2e1d45dce1a5836ffa616de63e", "reference": "237faae9a60a4a2e1d45dce1a5836ffa616de63e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.6.0"}, "conflict": {"composer/installers": "<1.0.6"}, "require-dev": {"composer/composer": "^1.0 || ^2.0", "phpunit/phpunit": ">=5.7.27"}, "type": "composer-plugin", "extra": {"class": "johnpbloch\\Composer\\WordPressCorePlugin"}, "autoload": {"psr-0": {"johnpbloch\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A custom installer to handle deploying WordPress with composer", "keywords": ["wordpress"], "support": {"issues": "https://github.com/johnpbloch/wordpress-core-installer/issues", "source": "https://github.com/johnpbloch/wordpress-core-installer/tree/master"}, "time": "2020-04-16T21:44:57+00:00"}, {"name": "wpackagist-plugin/insert-php", "version": "2.4.10", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/insert-php/", "reference": "tags/2.4.10"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/insert-php.2.4.10.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/insert-php/"}, {"name": "wpackagist-plugin/disqus-comment-system", "version": "3.1.2", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/disqus-comment-system/", "reference": "tags/3.1.2"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/disqus-comment-system.3.1.2.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/disqus-comment-system/"}, {"name": "wpackagist-plugin/protect-schemaorg-markup-in-html-editor", "version": "0.6", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/protect-schemaorg-markup-in-html-editor/", "reference": "tags/0.6"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/protect-schemaorg-markup-in-html-editor.0.6.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/protect-schemaorg-markup-in-html-editor/"}, {"name": "wpackagist-plugin/wordfence", "version": "8.0.5", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/wordfence/", "reference": "tags/8.0.5"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/wordfence.8.0.5.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/wordfence/"}, {"name": "wpackagist-plugin/wp-sentry-integration", "version": "8.3.1", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/wp-sentry-integration/", "reference": "tags/8.3.1"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/wp-sentry-integration.8.3.1.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/wp-sentry-integration/"}, {"name": "wpackagist-plugin/redis-cache", "version": "2.6.3", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/redis-cache/", "reference": "tags/2.6.3"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/redis-cache.2.6.3.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/redis-cache/"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.2", "ext-dom": "*", "ext-libxml": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}