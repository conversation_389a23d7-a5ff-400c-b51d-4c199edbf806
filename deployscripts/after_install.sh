#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e
# Print each command before executing (helpful for debugging)
# set -x
# Pipefail causes a pipeline to return the exit status of the last command that exited with non-zero status
set -o pipefail

: '
Comment Section:
  Case condition, here first check the DeploymentGroupName which is added as a Stage name in the form tags on CodeDeploy Deployment Group
  and compare if it is dev, uat or prod environment and then set the stage variable accordingly.

  The Stage variable is used for KMS decrypt functionality and set the name of logs dynamically..
'

# Function to handle errors
error_handler() {
  local exit_code=$1
  local line_number=$2
  echo "ERROR: Command failed with exit code $exit_code at line $line_number"
  exit $exit_code
}

# Set up the error trap
trap 'error_handler $? $LINENO' ERR

# Validate DEPLOYMENT_GROUP_NAME
if [ -z "$DEPLOYMENT_GROUP_NAME" ]; then
  echo "ERROR: DEPLOYMENT_GROUP_NAME is not set"
  exit 1
fi

# Default value for stage
stage="unknown"

case $DEPLOYMENT_GROUP_NAME in
  'dev')
    stage=dev
    ;;
  'distributed-dev')
    stage=distributed-dev
    ;;
  'uat')
    stage=uat
    ;;
  'distributed-uat')
    stage=distributed-uat
    ;;
  'prod')
    stage=prod
    ;;
  *)
    echo "WARNING: Unknown DEPLOYMENT_GROUP_NAME '$DEPLOYMENT_GROUP_NAME'"
    ;;
esac

echo "Deployment stage is set to '$stage'"
#----------------------------------------------------------------------------------------------------------------------
# Get and Store Instance ID
#----------------------------------------------------------------------------------------------------------------------
# Step 1: Get the token
token=$(curl -s --request PUT "http://169.254.169.254/latest/api/token" \
  --header "X-aws-ec2-metadata-token-ttl-seconds: 21600")

# Verify token was retrieved
if [ -z "$token" ]; then
  echo "ERROR: Failed to retrieve EC2 metadata token"
  exit 1
fi

# Step 2: Use the token to get the instance ID
instance_id=$(curl -s --header "X-aws-ec2-metadata-token: $token" \
  http://169.254.169.254/latest/meta-data/instance-id)

# Verify instance ID was retrieved
if [ -z "$instance_id" ]; then
  echo "ERROR: Failed to retrieve instance ID"
  exit 1
fi

echo "Instance ID: $instance_id"

#----------------------------------------------------------------------------------------------------------------------
# Update logs setup
#----------------------------------------------------------------------------------------------------------------------
rm -f /tmp/awslog.json

LOG_GROUP=${LOG_GROUP:-"hle-${stage}-wordpress"}

cat >/tmp/awslog.json << AWSLOG
{
  "agent": {
    "metrics_collection_interval": 10
  },
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/opt/codedeploy-agent/deployment-root/deployment-logs/codedeploy-agent-deployments.log",
            "log_group_name": "${LOG_GROUP}",
            "log_stream_name": "${instance_id}/deployment-logs"
          },
          {
            "file_path": "/var/log/cloud-init-output.log",
            "log_group_name": "${LOG_GROUP}",
            "log_stream_name": "${instance_id}/cloud-init-output"
          },
          {
            "file_path": "/var/log/nginx/error.log",
            "log_group_name": "${LOG_GROUP}",
            "log_stream_name": "${instance_id}/nginx-error-log"
          },
          {
            "file_path": "/var/log/nginx/access.log",
            "log_group_name": "${LOG_GROUP}",
            "log_stream_name": "${instance_id}/nginx-access-log"
          },
          {
            "file_path": "/var/log/php-fpm/www-error.log",
            "log_group_name": "${LOG_GROUP}",
            "log_stream_name": "${instance_id}/php-fpm-error-log"
          }
        ]
      }
    }
  },
  "metrics": {
    "namespace": "hle-${stage}-wordpress-memory-metrics",
    "metrics_collected": {
      "mem": {
        "measurement": [
          "mem_used_percent",
          "mem_available_percent",
          "mem_total",
          "mem_free",
          "mem_used",
          "mem_cached",
          "mem_buffered"
        ]
      },
      "disk": {
        "measurement": [
          "used_percent"
        ],
        "resources": [
          "*"
        ]
      }
    },
    "append_dimensions": {
      "InstanceId": "${instance_id}"
    }
  }
}
AWSLOG

# Run cloudwatch agent command and check result
/opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -a fetch-config -m ec2 -s -c file:/tmp/awslog.json
echo "CloudWatch agent configuration applied"

####This section will be removed if we decide not to use DataDog at all in the future as well.
# # Datadog Nginx logs config
# rm -f /etc/datadog-agent/conf.d/nginx.d/conf.yaml
# tee /etc/datadog-agent/conf.d/nginx.d/conf.yaml > /dev/null <<EOF
# init_config:

# instances:

# logs:
#   - type: file
#     path: /var/log/nginx/access.log
#     service: ${LOG_GROUP}-access-log
#     source: nginx
#     sourcecategory: http_web_access
#     # Include additional optional parameters
#     # For example, log processing rules if needed
#     log_processing_rules:
#       - type: mask_sequences
#         name: mask_sensitive_data
#         replace_placeholder: "[masked]"
#         pattern: "password=[^&]*"

#   - type: file
#     path: /var/log/nginx/error.log
#     service: ${LOG_GROUP}-error-log
#     source: nginx
#     sourcecategory: http_web_error
# EOF

# Datadog PHP-FPM logs config
# tee /etc/datadog-agent/conf.d/php_fpm.d/conf.yaml > /dev/null <<EOF
# init_config:

# instances:

# logs:
#   # Collect PHP-FPM error logs
#   - type: file
#     path: /var/log/php-fpm/www-error.log
#     service: ${LOG_GROUP}-php-fpm
#     source: php
#     sourcecategory: php_fpm_error

# EOF

# tee /etc/datadog-agent/conf.d/systemd.d/conf.yaml > /dev/null <<EOF
# init_config:

# instances:

# logs:
#   # Collect system logs
#   - type: file
#     path: /var/log/syslog
#     service: ${LOG_GROUP}-system
#     source: syslog
#     sourcecategory: system

# EOF
# systemctl restart datadog-agent || { echo "Failed to restart datadog-agent"; exit 1; }
# echo "Datadog agent restarted"

#----------------------------------------------------------------------------------------------------------------------
# Copy php.ini file of repository
#----------------------------------------------------------------------------------------------------------------------
rm -rf /etc/php.ini
cp /var/www/wordpress-cd/server-config/php.ini /etc/php.ini || { echo "Failed to copy php.ini"; exit 1; }
echo "PHP configuration copied"

#----------------------------------------------------------------------------------------------------------------------
# Copy Nginx configuration and start server
#----------------------------------------------------------------------------------------------------------------------
echo "Nginx configuration Started"

mkdir -p /var/cache/nginx/nginx_cache
chown -R nginx:nginx /var/cache/nginx/nginx_cache

# Use stage-specific nginx configuration file if it exists
if [ "${stage}" == "prod" ] && [ -f /var/www/wordpress-cd/server-config/nginx-prod.conf ]; then
    echo "Using production Nginx configuration"
    mv /var/www/wordpress-cd/server-config/nginx-prod.conf /etc/nginx/nginx.conf || { echo "Failed to move nginx-prod.conf"; exit 1; }
    #copying the reverse-proxy specific files
    mv /var/www/wordpress-cd/server-config/reverse-proxy-prod.conf /etc/nginx/reverse-proxy-prod.conf || { echo "Failed to move reverse-proxy-prod.conf"; exit 1; }
    mv /var/www/wordpress-cd/server-config/common-reverse-proxy-config.conf /etc/nginx/common-reverse-proxy-config.conf || { echo "Failed to move common.conf"; exit 1; }

elif { [ "${stage}" == "uat" ] || [ "${stage}" == "distributed-uat" ]; } && [ -f /var/www/wordpress-cd/server-config/nginx-uat.conf ]; then
    echo "Using UAT Nginx configuration"
    mv /var/www/wordpress-cd/server-config/nginx-uat.conf /etc/nginx/nginx.conf || { echo "Failed to move nginx-uat.conf"; exit 1; }
    #copying the reverse-proxy specific files
    mv /var/www/wordpress-cd/server-config/reverse-proxy-uat.conf /etc/nginx/reverse-proxy-uat.conf || { echo "Failed to move reverse-proxy-uat.conf"; exit 1; }
    mv /var/www/wordpress-cd/server-config/common-reverse-proxy-config.conf /etc/nginx/common-reverse-proxy-config.conf || { echo "Failed to move common.conf"; exit 1; }

else
    echo "Using default development Nginx configuration"
    mv /var/www/wordpress-cd/server-config/nginx-dev.conf /etc/nginx/nginx.conf || { echo "Failed to move nginx-dev.conf"; exit 1; }
    #copying the reverse-proxy specific files
    mv /var/www/wordpress-cd/server-config/reverse-proxy-dev.conf /etc/nginx/reverse-proxy-dev.conf || { echo "Failed to move reverse-proxy-dev.conf"; exit 1; }
    mv /var/www/wordpress-cd/server-config/common-reverse-proxy-config.conf /etc/nginx/common-reverse-proxy-config.conf || { echo "Failed to move common.conf"; exit 1; }
fi

echo "Completed Nginx configuration"

#----------------------------------------------------------------------------------------------------------------------
# Composer install
#----------------------------------------------------------------------------------------------------------------------
# Copy wordpress core and external plugins to source
cd /var/www/wordpress-cd || { echo "Failed to change directory to /var/www/wordpress-cd"; exit 1; }
pwd
ls -la
export COMPOSER_ALLOW_SUPERUSER=1
composer install --ignore-platform-reqs --no-dev || { echo "Composer install failed"; exit 1; }
composer update || { echo "Composer update failed"; exit 1; }
echo "Composer dependencies installed"

mkdir -p /var/www/html/wp-content/uploads
mkdir -p /var/www/html/wp-content/wflogs

# Check if EFS is already mounted before attempting unmount
if mountpoint -q /var/www/html/wp-content/uploads; then
    umount -l /var/www/html/wp-content/uploads
    echo "Unmount completed"
fi

#----------------------------------------------------------------------------------------------------------------------
# Minification of JS and CSS
#----------------------------------------------------------------------------------------------------------------------
# Ensure uglifyjs and cleancss are installed
command -v uglifyjs >/dev/null 2>&1 || { echo "ERROR: uglifyjs not found! Install it first."; exit 1; }
command -v cleancss >/dev/null 2>&1 || { echo "ERROR: cleancss not found! Install it first."; exit 1; }

# Minify JavaScript files
js_path="/var/www/wordpress-cd/wp-content/themes/homeloanexperts/hle-website-redesign/assets/js"
css_path="/var/www/wordpress-cd/wp-content/themes/homeloanexperts/hle-website-redesign/assets/css"

# Check if there are any JS files
shopt -s nullglob  # Prevents *.js from being passed as a literal if no files exist
js_files=($js_path/*.js)

if [[ ${#js_files[@]} -gt 0 ]]; then
    for file in "${js_files[@]}"; do
        if [[ ! "$file" =~ \.min\.js$ ]]; then
            echo "Minifying $file..."
            uglifyjs "$file" -o "${file%.js}.min.js" --source-map || { echo "Failed to minify $file"; exit 1; }
        fi
    done
else
    echo "No JavaScript files found in $js_path"
fi

# Minify CSS files
shopt -s nullglob  # Prevents *.css from being passed as a literal if no files exist
css_files=($css_path/*.css)

if [[ ${#css_files[@]} -gt 0 ]]; then
    for file in "${css_files[@]}"; do
        if [[ ! "$file" =~ \.min\.css$ ]]; then
            echo "Minifying $file..."
            cleancss -o "${file%.*}.min.css" "$file" || { echo "Failed to minify $file"; exit 1; }
        fi
    done
else
    echo "No CSS files found in $css_path"
fi

echo "Minification complete."

#----------------------------------------------------------------------------------------------------------------------
# Decrypt wp-config files
#----------------------------------------------------------------------------------------------------------------------
if [ "${stage}" == "distributed-dev" ]; then
  # Retrieve and Store efs id from parameter_store
  EFS_VOLUME=$(aws --region=ap-southeast-2 ssm get-parameter --name "hle-dev-wordpress-efs-id" --with-decryption --output text --query Parameter.Value)
  if [ -z "$EFS_VOLUME" ]; then
    echo "ERROR: Failed to retrieve EFS volume ID for ${stage}"
    exit 1
  fi
  echo "EFS ID = ${EFS_VOLUME}"

  # Decrypt wp-config file
  aws kms decrypt --ciphertext-blob fileb:///var/www/wordpress-cd/wp-config.lambda.encrypted.dev.php --region ap-southeast-2 --output text --query Plaintext | base64 --decode > /var/www/wordpress-cd/wp-config.php || { echo "Failed to decrypt wp-config"; exit 1; }
  echo "${stage} KMS Decrypt Complete on wp-config"

  # Decrypt config file for LDAS
  aws kms decrypt --ciphertext-blob fileb:///var/www/wordpress-cd/ldas/v1/ldas-config.lambda.encrypted.dev.php --region ap-southeast-2 --output text --query Plaintext | base64 --decode > /var/www/wordpress-cd/ldas/v1/ldas-config.php || { echo "Failed to decrypt ldas-config"; exit 1; }
  echo "Completed ${stage} ldas config"

elif [ "${stage}" == "distributed-uat" ]; then
  # Retrieve and Store efs id from parameter_store
  EFS_VOLUME=$(aws --region=ap-southeast-2 ssm get-parameter --name "hle-uat-wordpress-efs-id" --with-decryption --output text --query Parameter.Value)
  if [ -z "$EFS_VOLUME" ]; then
    echo "ERROR: Failed to retrieve EFS volume ID for ${stage}"
    exit 1
  fi
  echo "EFS ID = ${EFS_VOLUME}"
  
  # Decrypt wp-config file
  aws kms decrypt --ciphertext-blob fileb:///var/www/wordpress-cd/wp-config.lambda.encrypted.uat.php --region ap-southeast-2 --output text --query Plaintext | base64 --decode > /var/www/wordpress-cd/wp-config.php || { echo "Failed to decrypt wp-config"; exit 1; }
  echo " ${stage} KMS Decrypt Complete on wp-config"

  # Decrypt config file for LDAS
  aws kms decrypt --ciphertext-blob fileb:///var/www/wordpress-cd/ldas/v1/ldas-config.lambda.encrypted.uat.php --region ap-southeast-2 --output text --query Plaintext | base64 --decode > /var/www/wordpress-cd/ldas/v1/ldas-config.php || { echo "Failed to decrypt ldas-config"; exit 1; }
  echo "Completed ${stage} ldas config"

else
  # Retrieve and Store efs id from parameter_store
  EFS_VOLUME=$(aws --region=ap-southeast-2 ssm get-parameter --name "hle-${stage}-wordpress-efs-id" --with-decryption --output text --query Parameter.Value)
  if [ -z "$EFS_VOLUME" ]; then
    echo "ERROR: Failed to retrieve EFS volume ID for ${stage}"
    exit 1
  fi
  echo "EFS ID = ${EFS_VOLUME}"
  
  # Decrypt wp-config file
  aws kms decrypt --ciphertext-blob fileb:///var/www/wordpress-cd/wp-config.encrypted.${stage}.php --region ap-southeast-2 --output text --query Plaintext | base64 --decode > /var/www/wordpress-cd/wp-config.php || { echo "Failed to decrypt wp-config"; exit 1; }
  echo " ${stage} KMS Decrypt Complete on wp-config"
  
  # Decrypt config file for LDAS
  aws kms decrypt --ciphertext-blob fileb:///var/www/wordpress-cd/ldas/v1/${stage}.ldas-config.encrypted.php --region ap-southeast-2 --output text --query Plaintext | base64 --decode > /var/www/wordpress-cd/ldas/v1/ldas-config.php || { echo "Failed to decrypt ldas-config"; exit 1; }
  echo "Completed ${stage} ldas config"
fi

#----------------------------------------------------------------------------------------------------------------------
# Sync the changes from Wordpress Deployed folder into Root folder i.e /var/www/html
#----------------------------------------------------------------------------------------------------------------------
chown -R nginx:nginx /var/www/wordpress-cd/ || { echo "Failed to change ownership of /var/www/wordpress-cd/"; exit 1; }
chown -R nginx:nginx /usr/lib/systemd/system/nginx.service || { echo "Failed to change ownership of nginx.service"; exit 1; }
sudo systemctl daemon-reload || { echo "Failed to reload systemd"; exit 1; }
echo "Nginx Ownership Changed"

chmod 2775 /var/www/wordpress-cd/ || { echo "Failed to change mode of /var/www/wordpress-cd/"; exit 1; }
# More secure approach to permissions
find /var/www/wordpress-cd/ -type d -exec chmod 755 {} \; || { echo "Failed to set directory permissions"; exit 1; }
find /var/www/wordpress-cd/ -type f -exec chmod 644 {} \; || { echo "Failed to set file permissions"; exit 1; }
echo "Nginx Change mode completed"

echo "Rsync Started"
rsync -a --ignore-times --progress /var/www/wordpress-cd/ /var/www/html || { echo "Failed to rsync /var/www/wordpress-cd/ to /var/www/html"; exit 1; }
rsync -a --ignore-times --progress /var/www/html/wordpress/ /var/www/html || { echo "Failed to rsync /var/www/html/wordpress/ to /var/www/html"; exit 1; }
cd /var/www/html || { echo "Failed to change directory to /var/www/html"; exit 1; }
rm -rf wordpress
echo "Rsync completed"

#----------------------------------------------------------------------------------------------------------------------
# Verify and update robots.txt for dev and uat
#----------------------------------------------------------------------------------------------------------------------
if [[ "${stage}" == "dev" || "${stage}" == "distributed-dev" || "${stage}" == "distributed-uat" || "${stage}" == "uat" ]]; then
  echo "User-agent: * " > /var/www/html/robots.txt
  echo "Disallow: / " >> /var/www/html/robots.txt

  echo "${stage} robots.txt updated"
fi

#----------------------------------------------------------------------------------------------------------------------

mount -t efs ${EFS_VOLUME}:/ /var/www/html/wp-content/uploads || { echo "Failed to mount EFS volume"; exit 1; }
echo "Mount completed"

# Email signature moved to themes folder from uploads to help designers push their file in git repo
rm -rf /var/www/html/wp-content/uploads/emailSignature
ln -s /var/www/html/wp-content/themes/homeloanexperts/assets/emailSignatures/ /var/www/html/wp-content/uploads/emailSignature || { echo "Failed to create symlink for email signatures"; exit 1; }

#####This section will be removed if we decide not to use DataDog at all in the future as well.
# echo "Datadog log Permission configuration Started"
# usermod -a -G adm dd-agent || { echo "Failed to add dd-agent to adm group"; exit 1; }

# # Fix the chown command by specifying the user and group for /var/log
# chown -Rv dd-agent:adm /var/log/ || { echo "Failed to change ownership of /var/log/"; exit 1; }
# chown -Rv dd-agent:adm /var/log/nginx/ || { echo "Failed to change ownership of /var/log/nginx/"; exit 1; }
# chmod 644 /var/log/nginx/ || { echo "Failed to set permissions on /var/log/nginx/"; exit 1; }
# chmod 755 /var/log/nginx/ || { echo "Failed to set permissions on /var/log/nginx/"; exit 1; }
# echo "Datadog log Permission configuration completed"

#----------------------------------------------------------------------------------------------------------------------
# Redis Cache enable after deployment
#----------------------------------------------------------------------------------------------------------------------
cd /var/www/html || { echo "Failed to change directory to /var/www/html"; exit 1; }
curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar || { echo "Failed to download wp-cli.phar"; exit 1; }
chmod +x wp-cli.phar || { echo "Failed to make wp-cli.phar executable"; exit 1; }
chown -R nginx:nginx wp-cli.phar || { echo "Failed to change ownership of wp-cli.phar"; exit 1; }

# First, check if Redis Object Cache plugin is installed, if not, install it
php wp-cli.phar plugin install redis-cache --activate --allow-root || { echo "Failed to install Redis Cache plugin"; exit 1; }

# Then enable Redis using the correct command
php wp-cli.phar redis enable --allow-root || { echo "Failed to enable Redis cache"; exit 1; }

echo "Redis cache enable complete"

#----------------------------------------------------------------------------------------------------------------------
# Ensure httpd service starts only if not running, else restart
#----------------------------------------------------------------------------------------------------------------------
echo "Checking Nginx service status"
if ! systemctl is-active --quiet nginx; then
  echo "nginx service is not running, starting the service"
  nginx -t || { echo "Nginx configuration test failed"; exit 1; }
  systemctl enable nginx || { echo "Failed to enable nginx service"; exit 1; }
  service nginx start || { echo "Failed to start nginx service"; exit 1; }
  echo "Successfully started Nginx service"
else
  echo "Nginx service is already running, restarting the service"
  systemctl enable nginx || { echo "Failed to enable nginx service"; exit 1; }
  service nginx restart || { echo "Failed to restart nginx service"; exit 1; }
  echo "Successfully restarted Nginx service"
fi

# Check the final echo statement
echo "Script execution completed successfully."